Test subset1_20 (status) - passed
Test subset2_21 (branch) - failed (Incorrect output)
Your program produced these 5 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ mygit-branch
* trunk
*** TEST STOPPED: incorrect output from mygit command

The correct 25 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ mygit-branch
mygit-branch: error: this command can not be run until after the first commit
$ touch a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch branch1
$ mygit-branch branch2
$ mygit-branch trunk
mygit-branch: error: branch 'trunk' already exists
$ mygit-branch
branch1
branch2
trunk
$ mygit-branch -d branch2
Deleted branch 'branch2'
$ mygit-branch -d trunk
mygit-branch: error: can not delete branch 'trunk': default branch
$ mygit-branch -d b1
mygit-branch: error: branch 'b1' doesn't exist
$ mygit-branch
branch1
trunk

The difference between your output(-) and the correct output(+) is:
...
  $ mygit-branch
+ mygit-branch: error: this command can not be run until after the first commit
+ $ touch a
+ $ mygit-add a
+ $ mygit-commit -m commit-0
+ Committed as commit 0
+ $ mygit-branch branch1
+ $ mygit-branch branch2
+ $ mygit-branch trunk
+ mygit-branch: error: branch 'trunk' already exists
+ $ mygit-branch
+ branch1
+ branch2
- * trunk
? --

+ trunk
- *** TEST STOPPED: incorrect output from mygit command
+ $ mygit-branch -d branch2
+ Deleted branch 'branch2'
+ $ mygit-branch -d trunk
+ mygit-branch: error: can not delete branch 'trunk': default branch
+ $ mygit-branch -d b1
+ mygit-branch: error: branch 'b1' doesn't exist
+ $ mygit-branch
+ branch1
+ trunk
Test subset1_22 (rm add rm show) - failed (Incorrect output)
Your program produced these 12 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-rm a
$ mygit-status
a - file deleted, deleted from index
$ mygit-commit -m commit-1
mygit-commit: error: nothing to commit
*** TEST STOPPED: incorrect output from mygit command

The correct 35 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-rm a
$ mygit-status
a - file deleted, deleted from index
$ mygit-commit -m commit-1
Committed as commit 1
$ mygit-status
$ echo world >a
$ mygit-status
a - untracked
$ mygit-commit -m commit-2
nothing to commit
$ mygit-add a
$ mygit-commit -m commit-2
Committed as commit 2
$ mygit-rm a
$ mygit-commit -m commit-3
Committed as commit 3
$ mygit-show :a
mygit-show: error: 'a' not found in index
$ mygit-show 0:a
hello
$ mygit-show 1:a
mygit-show: error: 'a' not found in commit 1
$ mygit-show 2:a
world
$ mygit-show 3:a
mygit-show: error: 'a' not found in commit 3
$ mygit-show 4:a
mygit-show: error: unknown commit '4'

The difference between your output(-) and the correct output(+) is:
...
  $ mygit-commit -m commit-1
- mygit-commit: error: nothing to commit
- *** TEST STOPPED: incorrect output from mygit command
+ Committed as commit 1
+ $ mygit-status
+ $ echo world >a
+ $ mygit-status
+ a - untracked
+ $ mygit-commit -m commit-2
+ nothing to commit
+ $ mygit-add a
+ $ mygit-commit -m commit-2
+ Committed as commit 2
+ $ mygit-rm a
+ $ mygit-commit -m commit-3
+ Committed as commit 3
+ $ mygit-show :a
+ mygit-show: error: 'a' not found in index
+ $ mygit-show 0:a
+ hello
+ $ mygit-show 1:a
+ mygit-show: error: 'a' not found in commit 1
+ $ mygit-show 2:a
+ world
+ $ mygit-show 3:a
+ mygit-show: error: 'a' not found in commit 3
+ $ mygit-show 4:a
+ mygit-show: error: unknown commit '4'
Test subset1_23 (add commit status change/rm) - failed (Incorrect output)
Your program produced these 20 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hi >a
$ mygit-add a
$ mygit-commit -m message
Committed as commit 0
$ echo hello >b
$ echo hola >c
$ mygit-add b c
$ mygit-status
a - same as repo
b - added to index
c - added to index
$ echo there >>b
$ rm c
$ mygit-status
a - same as repo
b - added to index
c - file deleted
*** TEST STOPPED: incorrect output from mygit command

The correct 19 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hi >a
$ mygit-add a
$ mygit-commit -m message
Committed as commit 0
$ echo hello >b
$ echo hola >c
$ mygit-add b c
$ mygit-status
a - same as repo
b - added to index
c - added to index
$ echo there >>b
$ rm c
$ mygit-status
a - same as repo
b - added to index, file changed
c - added to index, file deleted

The difference between your output(-) and the correct output(+) is:
...
  a - same as repo
+ b - added to index, file changed
+ c - added to index, file deleted
- b - added to index
- c - file deleted
- *** TEST STOPPED: incorrect output from mygit command
Test subset2_24 (checkout) - failed (Incorrect output)
Your program produced these 16 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ touch a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ touch b
$ mygit-add b
$ mygit-commit -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
*** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b

The correct 32 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ touch a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ touch b
$ mygit-add b
$ mygit-commit -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-branch b2
$ mygit-checkout b2
Switched to branch 'b2'
$ touch c
$ mygit-add c
$ mygit-commit -m commit-2
Committed as commit 2
$ mygit-branch
b1
b2
trunk
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout non-existent-branch
mygit-checkout: error: unknown branch 'non-existent-branch'

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'trunk'
- *** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b
+ $ mygit-branch b2
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ touch c
+ $ mygit-add c
+ $ mygit-commit -m commit-2
+ Committed as commit 2
+ $ mygit-branch
+ b1
+ b2
+ trunk
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout non-existent-branch
+ mygit-checkout: error: unknown branch 'non-existent-branch'
Test subset2_25 (branch commit checkout) - failed (Incorrect output)
Your program produced these 15 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo line 1 >a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ echo line 2 >>a
$ echo hello >b
$ mygit-add a b
$ mygit-commit -m commit-1
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
*** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b

The correct 28 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo line 1 >a
$ mygit-add a
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ echo line 2 >>a
$ echo hello >b
$ mygit-add a b
$ mygit-commit -m commit-1
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
$ echo line 3 >>a
$ echo world >b
$ touch c
$ mygit-add a b c
$ mygit-commit -m commit-2
Committed as commit 2
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout b1
Switched to branch 'b1'

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'b1'
- *** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b
+ $ echo line 3 >>a
+ $ echo world >b
+ $ touch c
+ $ mygit-add a b c
+ $ mygit-commit -m commit-2
+ Committed as commit 2
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout b1
+ Switched to branch 'b1'
Test subset2_26 (checkout modified file) - failed (Incorrect output)
Your program produced these 18 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ echo world >>a
$ mygit-checkout b1
Switched to branch 'b1'
*** TEST STOPPED - INCORECT FILE: file 'a' has incorrect contents
*** 'a' contents are:
hello

*** 'a' contents should be:
hello
world


The correct 33 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ echo world >>a
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-status
a - file changed, changes not staged for commit
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-add a
$ mygit-status
a - file changed, changes staged for commit
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-status
a - file changed, changes staged for commit
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-commit -a -m commit-B
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-status
a - same as repo
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-status
a - same as repo

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'b1'
- *** TEST STOPPED - INCORECT FILE: file 'a' has incorrect contents
- *** 'a' contents are:
- hello
- 
- *** 'a' contents should be:
- hello
- world
- 
+ $ mygit-status
+ a - file changed, changes not staged for commit
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-add a
+ $ mygit-status
+ a - file changed, changes staged for commit
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-status
+ a - file changed, changes staged for commit
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-commit -a -m commit-B
+ Committed as commit 1
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-status
+ a - same as repo
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-status
+ a - same as repo
Test subset2_27 (checkout rm) - failed (Incorrect output)
Your program produced these 13 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ touch a b c
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ touch d e
$ mygit-rm a b
mygit-rm: error: 'b' is not in the mygit repository
*** TEST STOPPED - MISSING FILES:: these files were not present and should have been: a

The correct 43 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ touch a b c
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ touch d e
$ mygit-rm a b
mygit-rm: error: 'b' is not in the mygit repository
$ mygit-commit -m commit-B
nothing to commit
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-branch b2
$ mygit-checkout b2
Switched to branch 'b2'
$ touch f g
$ mygit-rm b c
mygit-rm: error: 'b' is not in the mygit repository
$ mygit-add f g
$ mygit-commit -m commit-C
Committed as commit 1
$ mygit-branch
b1
b2
trunk
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout b2
Switched to branch 'b2'
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout b2
Switched to branch 'b2'
$ mygit-checkout b1
Switched to branch 'b1'

The difference between your output(-) and the correct output(+) is:
...
  mygit-rm: error: 'b' is not in the mygit repository
- *** TEST STOPPED - MISSING FILES:: these files were not present and should have been: a
+ $ mygit-commit -m commit-B
+ nothing to commit
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-branch b2
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ touch f g
+ $ mygit-rm b c
+ mygit-rm: error: 'b' is not in the mygit repository
+ $ mygit-add f g
+ $ mygit-commit -m commit-C
+ Committed as commit 1
+ $ mygit-branch
+ b1
+ b2
+ trunk
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ mygit-checkout b1
+ Switched to branch 'b1'
Test subset2_28 (delete branch with unmerged work) - failed (Incorrect output)
Your program produced these 16 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch branch1
$ mygit-checkout branch1
Switched to branch 'branch1'
$ echo world >b
$ mygit-add b
$ mygit-commit -a -m commit-B
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
*** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b

The correct 23 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch branch1
$ mygit-checkout branch1
Switched to branch 'branch1'
$ echo world >b
$ mygit-add b
$ mygit-commit -a -m commit-B
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-branch -d branch1
mygit-branch: error: branch 'branch1' has unmerged changes
$ mygit-merge branch1 -m merge-message
Fast-forward: no commit created
$ mygit-branch -d branch1
Deleted branch 'branch1'
$ mygit-branch
trunk

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'trunk'
- *** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b
+ $ mygit-branch -d branch1
+ mygit-branch: error: branch 'branch1' has unmerged changes
+ $ mygit-merge branch1 -m merge-message
+ Fast-forward: no commit created
+ $ mygit-branch -d branch1
+ Deleted branch 'branch1'
+ $ mygit-branch
+ trunk
Test subset2_29 (checkout with work that would be over-written) - failed (Incorrect output)
Your program produced these 14 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch branchA
$ echo world >b
$ mygit-add b
$ mygit-commit -m commit-B
Committed as commit 1
$ mygit-checkout branchA
Switched to branch 'branchA'
*** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b

The correct 22 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch branchA
$ echo world >b
$ mygit-add b
$ mygit-commit -m commit-B
Committed as commit 1
$ mygit-checkout branchA
Switched to branch 'branchA'
$ echo new contents >b
$ mygit-checkout trunk
mygit-checkout: error: Your changes to the following files would be overwritten by checkout:
b
$ mygit-add b
$ mygit-commit -m commit-C
Committed as commit 2
$ mygit-checkout trunk
Switched to branch 'trunk'

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'branchA'
- *** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: b
+ $ echo new contents >b
+ $ mygit-checkout trunk
+ mygit-checkout: error: Your changes to the following files would be overwritten by checkout:
+ b
+ $ mygit-add b
+ $ mygit-commit -m commit-C
+ Committed as commit 2
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
Test subset2_30 (successful merge) - passed
Test subset2_31 (successful merge - multiple files) - failed (Incorrect output)
Your program produced these 21 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq -f "line %.0f" 1 7 >a
$ seq -f "line %.0f" 1 7 >b
$ seq -f "line %.0f" 1 7 >c
$ seq -f "line %.0f" 1 7 >d
$ mygit-add a b c d
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ seq -f "line %.0f" 0 7 >a
$ seq -f "line %.0f" 1 8 >b
$ seq -f "line %.0f" 1 7 >e
$ mygit-add e
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
*** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: e

The correct 40 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq -f "line %.0f" 1 7 >a
$ seq -f "line %.0f" 1 7 >b
$ seq -f "line %.0f" 1 7 >c
$ seq -f "line %.0f" 1 7 >d
$ mygit-add a b c d
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ seq -f "line %.0f" 0 7 >a
$ seq -f "line %.0f" 1 8 >b
$ seq -f "line %.0f" 1 7 >e
$ mygit-add e
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -i 4d c
$ seq -f "line %.0f" 0 8 >d
$ seq -f "line %.0f" 1 7 >f
$ mygit-add f
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge1
Committed as commit 3
$ mygit-log
3 merge1
2 commit-2
1 commit-1
0 commit-0
$ mygit-status
a - same as repo
b - same as repo
c - same as repo
d - same as repo
e - same as repo
f - same as repo

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'trunk'
- *** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: e
+ $ sed -i 4d c
+ $ seq -f "line %.0f" 0 8 >d
+ $ seq -f "line %.0f" 1 7 >f
+ $ mygit-add f
+ $ mygit-commit -a -m commit-2
+ Committed as commit 2
+ $ mygit-merge b1 -m merge1
+ Committed as commit 3
+ $ mygit-log
+ 3 merge1
+ 2 commit-2
+ 1 commit-1
+ 0 commit-0
+ $ mygit-status
+ a - same as repo
+ b - same as repo
+ c - same as repo
+ d - same as repo
+ e - same as repo
+ f - same as repo
Test subset2_32 (merge conflict) - failed (Incorrect output)
Your program produced these 25 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq 1 7 >7.txt
$ mygit-add 7.txt
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ sed -Ei s/2/42/ 7.txt
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -Ei s/5/24/ 7.txt
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge-message
mygit-merge: error: These files can not be merged:
7.txt
$ mygit-log
2 commit-2
1 commit-1
0 commit-0
*** TEST STOPPED: incorrect output from mygit command

The correct 25 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq 1 7 >7.txt
$ mygit-add 7.txt
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ sed -Ei s/2/42/ 7.txt
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -Ei s/5/24/ 7.txt
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge-message
mygit-merge: error: These files can not be merged:
7.txt
$ mygit-log
2 commit-2
0 commit-0
$ mygit-status
7.txt - same as repo

The difference between your output(-) and the correct output(+) is:
...
  2 commit-2
- 1 commit-1
  0 commit-0
- *** TEST STOPPED: incorrect output from mygit command
+ $ mygit-status
+ 7.txt - same as repo
Test subset2_33 (merge errors) - failed (Incorrect output)
Your program produced these 20 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq 1 7 >7.txt
$ mygit-add 7.txt
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ sed -Ei s/2/42/ 7.txt
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -Ei s/5/24/ 7.txt
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1
mygit-merge: error: usage: mygit-merge (branch-name|commit-number) -m message
*** TEST STOPPED: incorrect output from mygit command

The correct 24 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq 1 7 >7.txt
$ mygit-add 7.txt
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ sed -Ei s/2/42/ 7.txt
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -Ei s/5/24/ 7.txt
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1
mygit-merge: error: empty commit message
$ mygit-merge non-existent-branch -m message
mygit-merge: error: unknown branch 'non-existent-branch'
$ mygit-merge b1 -m message
mygit-merge: error: These files can not be merged:
7.txt

The difference between your output(-) and the correct output(+) is:
...
  $ mygit-merge b1
- mygit-merge: error: usage: mygit-merge (branch-name|commit-number) -m message
- *** TEST STOPPED: incorrect output from mygit command
+ mygit-merge: error: empty commit message
+ $ mygit-merge non-existent-branch -m message
+ mygit-merge: error: unknown branch 'non-existent-branch'
+ $ mygit-merge b1 -m message
+ mygit-merge: error: These files can not be merged:
+ 7.txt
Test subset2_34 (many branches) - failed (Incorrect output)
Your program produced these 17 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo 0 >level0
$ mygit-add level0
$ mygit-commit -m root
Committed as commit 0
$ mygit-branch b0
$ mygit-branch b1
$ mygit-checkout b0
Switched to branch 'b0'
$ echo 0 >level1
$ mygit-add level1
$ mygit-commit -m 0
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
*** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: level1

The correct 83 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo 0 >level0
$ mygit-add level0
$ mygit-commit -m root
Committed as commit 0
$ mygit-branch b0
$ mygit-branch b1
$ mygit-checkout b0
Switched to branch 'b0'
$ echo 0 >level1
$ mygit-add level1
$ mygit-commit -m 0
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
$ echo 1 >level1
$ mygit-add level1
$ mygit-commit -m 1
Committed as commit 2
$ mygit-checkout b0
Switched to branch 'b0'
$ mygit-branch b00
$ mygit-branch b01
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-branch b10
$ mygit-branch b11
$ mygit-checkout b00
Switched to branch 'b00'
$ echo 00 >level2
$ mygit-add level2
$ mygit-commit -m 00
Committed as commit 3
$ mygit-checkout b01
Switched to branch 'b01'
$ echo 01 >level2
$ mygit-add level2
$ mygit-commit -m 01
Committed as commit 4
$ mygit-checkout b10
Switched to branch 'b10'
$ echo 10 >level2
$ mygit-add level2
$ mygit-commit -m 10
Committed as commit 5
$ mygit-checkout b11
Switched to branch 'b11'
$ echo 11 >level2
$ mygit-add level2
$ mygit-commit -m 11
Committed as commit 6
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-log
0 root
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-log
2 1
0 root
$ mygit-checkout b01
Switched to branch 'b01'
$ mygit-log
4 01
1 0
0 root
$ mygit-checkout b11
Switched to branch 'b11'
$ mygit-log
6 11
2 1
0 root
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-merge b0 -m merge0
Fast-forward: no commit created
$ mygit-merge b00 -m merge00
Fast-forward: no commit created
$ mygit-log
3 00
1 0
0 root

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'b1'
- *** TEST STOPPED - UNEXPECTED FILES: these files were present and should not have been: level1
+ $ echo 1 >level1
+ $ mygit-add level1
+ $ mygit-commit -m 1
+ Committed as commit 2
+ $ mygit-checkout b0
+ Switched to branch 'b0'
+ $ mygit-branch b00
+ $ mygit-branch b01
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-branch b10
+ $ mygit-branch b11
+ $ mygit-checkout b00
+ Switched to branch 'b00'
+ $ echo 00 >level2
+ $ mygit-add level2
+ $ mygit-commit -m 00
+ Committed as commit 3
+ $ mygit-checkout b01
+ Switched to branch 'b01'
+ $ echo 01 >level2
+ $ mygit-add level2
+ $ mygit-commit -m 01
+ Committed as commit 4
+ $ mygit-checkout b10
+ Switched to branch 'b10'
+ $ echo 10 >level2
+ $ mygit-add level2
+ $ mygit-commit -m 10
+ Committed as commit 5
+ $ mygit-checkout b11
+ Switched to branch 'b11'
+ $ echo 11 >level2
+ $ mygit-add level2
+ $ mygit-commit -m 11
+ Committed as commit 6
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-log
+ 0 root
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-log
+ 2 1
+ 0 root
+ $ mygit-checkout b01
+ Switched to branch 'b01'
+ $ mygit-log
+ 4 01
+ 1 0
+ 0 root
+ $ mygit-checkout b11
+ Switched to branch 'b11'
+ $ mygit-log
+ 6 11
+ 2 1
+ 0 root
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-merge b0 -m merge0
+ Fast-forward: no commit created
+ $ mygit-merge b00 -m merge00
+ Fast-forward: no commit created
+ $ mygit-log
+ 3 00
+ 1 0
+ 0 root
14 tests passed 20 tests failed