<!DOCTYPE html>
<!-- saved from url=(0056)https://cgi.cse.unsw.edu.au/~cs9312/25T2/project/q1.html -->
<html lang="en" class=""><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>COMP9312 25T2 Project</title>
    <link href="./COMP9312 25T2 Project q1_files/bootstrap.min.css" rel="stylesheet" integrity="sha384-0evHe/X+R7YkIZDRvuzKMRqM+OrBnVFBL6DOitfPri4tjfHxaWutUpFmBp4vmVor" crossorigin="anonymous">
    <link rel="stylesheet" href="./COMP9312 25T2 Project q1_files/course.css">

<link type="text/css" rel="stylesheet" charset="UTF-8" href="./COMP9312 25T2 Project q1_files/m=el_main_css"></head>

<body>
    <header class="py-2">
        <div class="container">
            The University of New South Wales - COMP9312 - 25T2 - <strong>Data Analytics for Graphs</strong>
        </div>
    </header>

    <div class="container my-5 pb-5">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item fw-bold"><a href="https://cgi.cse.unsw.edu.au/~cs9312/25T2/project/index.html">Project Homepage</a></li>
                <li class="breadcrumb-item active fw-bold" aria-current="page">Q1</li>
            </ol>
        </nav>


        <h1 class="mt-5">Q1 <span class="fs-6">(10 marks)</span></h1>
        <p class="fw-light fs-4 mb-5">Design a solution to compute the <strong>top-k shortest simple paths</strong>
            between
            two vertices in a large directed weighted graph.</p>

        <figure style="text-align: center;">
            <img src="./COMP9312 25T2 Project q1_files/kssp.png" alt="Example Graph" style="max-width:60%; height:auto;">
            <figcaption>Figure&nbsp;1: Example Graph</figcaption>
        </figure>

        <h2>Problem Statement</h2>
        <p>In this question, you need to design an <strong>online algorithm</strong> to return the <strong>top-k
                shortest
                simple (loopless) paths</strong> between a given pair of vertices s and t in a <strong>large directed
                weighted graph</strong>. Your implementation should return k distinct paths ordered by increasing total
            path weight.</p>

        <p>In other words, any simple path from s to t that is not included in the top-k result must either have a
            greater total weight than the k-th returned path, or have the same weight but a later lexicographical order.
        </p>

        <h2>Background &amp; Marking Criteria</h2>
        <p>
            You are expected to implement an online algorithm that returns the <strong>top-k shortest simple
                paths</strong>
            between two vertices in a directed weighted graph.
            Each returned path must be:
        </p><ul>
            <li><strong>Simple</strong> — acyclic, with no repeated vertices</li>
            <li><strong>Distinct</strong> — no duplicate paths</li>
        </ul>
        <p></p>

        <p>
            You should aim to design an efficient solution that works on moderately large graphs. In addition to the
            implementation, a correct <strong>time complexity analysis is compulsory</strong> and will be marked.
        </p>

        <p>Marks will be awarded based on:</p>
        <ul>
            <li><strong>Correctness (autotests)</strong>: number of test cases passed</li>
            <li><strong>Theoretical analysis</strong>: clarity and correctness of your algorithm description and time
                complexity</li>
        </ul>

        <p>
            If your code is incomplete or fails some cases, you may still receive partial marks for a clear and
            well-reasoned explanation. Include your explanation in <code>Q1.pdf</code>, covering your algorithm design,
            time complexity, and any assumptions or limitations.
        </p>

        <h2>Example</h2>
        <p>
            Consider the graph shown in Figure 1. Given source vertex <i>s</i> and target vertex <i>t</i>, the top-k
            shortest simple paths are:
        </p>
        <ul>
            <li><b>k = 1</b>: [[s, a, b, c, d, e, t]]</li>
            <li><b>k = 2</b>: [[s, a, b, c, d, e, t], [s, g, t]]</li>
            <li><b>k = 3</b>: [[s, a, b, c, d, e, t], [s, g, t], [s, h, t]]</li>
            <li><b>k = 4</b>: [[s, a, b, c, d, e, t], [s, g, t], [s, h, t]]</li>
        </ul>

        <h2>Doing this Project</h2>

        <p>
            Open the code template file <a href="https://colab.research.google.com/drive/1ToJ18I4amEGSNT2lxA9SifoI0A3wnVfB?usp=sharing" target="_blank">Q1.ipynb</a> and make a copy in your own Google Drive. You need to implement a class
            named
            <code>KShortestPathsQuery</code> with a function <code>query(s, t, k)</code>.
        </p>

        <p>
            We will extract your <code>KShortestPathsQuery</code> class (along with any necessary import statements) and
            test it independently. Your implementation must run correctly without modification in the Colab environment.
        </p>

        <div class="highlight">
            <pre>################################################################################
# You can import any Python Standard Library modules.
################################################################################

class KShortestPathsQuery(object):
    def __init__(self):
        pass

    @staticmethod
    def query(G, s, t, k):
        ################################################################################
        # Input:
        #   s: source vertex
        #   t: target vertex
        #   k: number of shortest simple paths to return
        #
        # Output:
        #   A list of top-k shortest simple paths, each as a list of vertex IDs from s to t.
        #
        # Note:
        #   Each path must be simple (no repeated vertices) and distinct.
        #   Paths should be returned in ascending order of total weight.
        #   If multiple paths have the same weight, break ties by lexicographical order.
        #
        # Please analyze the time complexity of your solution.
        ################################################################################
        return []
</pre>
        </div>

        <h2>Required Files</h2>
        <p>
            Compress all related files for Q1 (<code>Q1.ipynb</code> and <code>Q1.pdf</code>, which contains your
            explanation) into <code>Q1.zip</code>, and submit it on Moodle.
        </p>

        <h2>Other Marking Criteria</h2>
        <ol style="list-style-type: lower-latin">
            <li>A correct and efficient implementation of an online algorithm with proper documentation will receive
                full marks.</li>
            <li>Partial marks will be awarded for implementations that pass only a subset of the hidden test cases.</li>
            <li>Submissions must return <strong>simple paths</strong>. Paths containing cycles or repeated vertices will
                be considered incorrect.</li>
            <li>You must provide a clear explanation of your algorithm and its time complexity.</li>
        </ol>

        <h2>Notes</h2>
        <ol style="list-style-type: lower-latin">
            <li>Your algorithm should handle edge cases such as unreachable targets and cases where fewer than k valid
                paths exist.</li>
            <li>Do not change the input/output format of the provided class or method.</li>
            <li>We will test your submission on larger graphs than those shown in the examples.</li>
            <li>If multiple simple paths have the same total distance, break ties by lexicographical order of vertex IDs
                in the path.
                For example, prefer path 1→2→5 over 1→3→5.</li>
            <li>You can not import any external libraries or modules other than the Python Standard Library.</li>
        </ol>

    </div>

    <footer class="pt-3 pb-5 mt-5">
        <div class="text-center fw-light">
            END OF QUESTION
        </div>
    </footer><div id="goog-gt-tt" class="VIpgJd-yAWNEb-L7lbkb skiptranslate" style="border-radius: 12px; margin: 0 0 0 -23px; padding: 0; font-family: &#39;Google Sans&#39;, Arial, sans-serif;" data-id=""><div id="goog-gt-vt" class="VIpgJd-yAWNEb-hvhgNd"><div class="VIpgJd-yAWNEb-hvhgNd-Ud7fr"><img src="./COMP9312 25T2 Project q1_files/24px.svg" width="24" height="24" alt=""><div class=" VIpgJd-yAWNEb-hvhgNd-IuizWc-i3jM8c " dir="ltr">原文</div></div><div class="VIpgJd-yAWNEb-hvhgNd-k77Iif"><div id="goog-gt-original-text" class="VIpgJd-yAWNEb-nVMfcd-fmcmS VIpgJd-yAWNEb-hvhgNd-axAV1"></div></div><div class="VIpgJd-yAWNEb-hvhgNd-N7Eqid ltr"><div class="VIpgJd-yAWNEb-hvhgNd-N7Eqid-B7I4Od ltr" dir="ltr"><div class="VIpgJd-yAWNEb-hvhgNd-UTujCb">请对此翻译评分</div><div class="VIpgJd-yAWNEb-hvhgNd-eO9mKe">您的反馈将用于改进谷歌翻译</div></div><div class="VIpgJd-yAWNEb-hvhgNd-xgov5 ltr"><button id="goog-gt-thumbUpButton" type="button" class="VIpgJd-yAWNEb-hvhgNd-bgm6sf" title="翻译质量很棒" aria-label="翻译质量很棒" aria-pressed="false"><span id="goog-gt-thumbUpIcon"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0S7.08 6.85 7 7H2v13h16c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73V9c0-1.1-.9-2-2-2zM7 18H4V9h3v9zm14-7l-3 7H9V8l4.34-4.34L12 9h9v2z"></path></svg></span><span id="goog-gt-thumbUpIconFilled"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M21 7h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 0S7.08 6.85 7 7v13h11c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73V9c0-1.1-.9-2-2-2zM5 7H1v13h4V7z"></path></svg></span></button><button id="goog-gt-thumbDownButton" type="button" class="VIpgJd-yAWNEb-hvhgNd-bgm6sf" title="翻译质量很差" aria-label="翻译质量很差" aria-pressed="false"><span id="goog-gt-thumbDownIcon"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M3 17h6.31l-.95 4.57-.03.32c0 .*********** 1.06L9.83 24s7.09-6.85 7.17-7h5V4H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2zM17 6h3v9h-3V6zM3 13l3-7h9v10l-4.34 4.34L12 15H3v-2z"></path></svg></span><span id="goog-gt-thumbDownIconFilled"><svg width="24" height="24" viewBox="0 0 24 24" focusable="false" class="VIpgJd-yAWNEb-hvhgNd-THI6Vb NMm5M"><path d="M3 17h6.31l-.95 4.57-.03.32c0 .*********** 1.06L9.83 24s7.09-6.85 7.17-7V4H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2zm16 0h4V4h-4v13z"></path></svg></span></button></div></div><div id="goog-gt-votingHiddenPane" class="VIpgJd-yAWNEb-hvhgNd-aXYTce"><form id="goog-gt-votingForm" action="https://translate.googleapis.com/translate_voting?client=te_lib" method="post" target="votingFrame" class="VIpgJd-yAWNEb-hvhgNd-aXYTce"><input type="text" name="sl" id="goog-gt-votingInputSrcLang"><input type="text" name="tl" id="goog-gt-votingInputTrgLang"><input type="text" name="query" id="goog-gt-votingInputSrcText"><input type="text" name="gtrans" id="goog-gt-votingInputTrgText"><input type="text" name="vote" id="goog-gt-votingInputVote"></form><iframe name="votingFrame" frameborder="0" src="./COMP9312 25T2 Project q1_files/saved_resource.html"></iframe></div></div></div>


</body></html>