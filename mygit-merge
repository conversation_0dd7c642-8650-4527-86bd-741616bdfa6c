#!/usr/bin/env python3

import os
import sys
import shutil
import time
import hashlib

def error_exit(message):
    print(f"mygit-merge: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        return -1
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        return int(commit_num) if commit_num != '-1' else -1

def get_commit_entries(commit_num):
    if commit_num == -1:
        return {}
    
    commit_index = f'.mygit/commits/{commit_num}/index'
    if not os.path.exists(commit_index):
        return {}
    
    entries = {}
    with open(commit_index, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    entries[parts[1]] = parts[0]
    return entries

def get_commit_count():
    with open('.mygit/commit_count', 'r') as f:
        count = int(f.read().strip())
    return count

def update_commit_count(count):
    with open('.mygit/commit_count', 'w') as f:
        f.write(str(count + 1) + '\n')

def merge_branch_or_commit(target, message):
    current_branch = get_current_branch()
    current_commit = get_branch_commit(current_branch)
    
    # 确定目标提交号
    if target.isdigit():
        # 目标是提交号
        target_commit = int(target)
        if not os.path.exists(f'.mygit/commits/{target_commit}'):
            error_exit(f"unknown commit '{target}'")
    else:
        # 目标是分支名
        if not os.path.exists(f'.mygit/branches/{target}'):
            error_exit(f"unknown branch '{target}'")
        target_commit = get_branch_commit(target)
        if target_commit == -1:
            error_exit(f"branch '{target}' has no commits")
    
    # 获取当前分支和目标的文件条目
    current_entries = get_commit_entries(current_commit)
    target_entries = get_commit_entries(target_commit)
    
    # 检查是否是fast-forward合并
    if current_commit < target_commit:
        # 检查是否可以fast-forward
        can_fast_forward = True
        for commit_num in range(current_commit + 1, target_commit + 1):
            if not os.path.exists(f'.mygit/commits/{commit_num}'):
                can_fast_forward = False
                break
        
        if can_fast_forward:
            # Fast-forward合并
            # 更新当前分支指向目标提交
            with open(f'.mygit/branches/{current_branch}', 'w') as f:
                f.write(str(target_commit) + '\n')
            
            # 更新索引和工作目录
            target_index = f'.mygit/commits/{target_commit}/index'
            if os.path.exists(target_index):
                shutil.copy2(target_index, '.mygit/index')
                
                # 更新工作目录
                with open(target_index, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            parts = line.split(' ', 1)
                            if len(parts) == 2:
                                file_hash, filename = parts
                                object_path = f'.mygit/objects/{file_hash}'
                                if os.path.exists(object_path):
                                    with open(object_path, 'r', encoding='utf-8') as src:
                                        with open(filename, 'w', encoding='utf-8') as dst:
                                            dst.write(src.read())
            
            print("Fast-forward: no commit created")
            return
    
    # 检查冲突 - 基于共同祖先的三路合并
    conflicts = []

    # 找到共同祖先（简化：使用commit 0作为基准）
    base_entries = get_commit_entries(0) if current_commit > 0 and target_commit > 0 else {}

    for filename in set(current_entries.keys()) | set(target_entries.keys()):
        current_hash = current_entries.get(filename)
        target_hash = target_entries.get(filename)
        base_hash = base_entries.get(filename)

        # 三路合并逻辑
        if current_hash and target_hash and current_hash != target_hash:
            # 两个分支都有这个文件，但内容不同
            if base_hash:
                # 有共同祖先
                current_changed = (current_hash != base_hash)
                target_changed = (target_hash != base_hash)

                # 只有当两个分支都修改了文件时才是冲突
                if current_changed and target_changed:
                    conflicts.append(filename)
                # 如果只有一个分支修改了，可以自动合并
            else:
                # 没有共同祖先，两个分支都创建了同名文件但内容不同
                conflicts.append(filename)
    
    if conflicts:
        print("mygit-merge: error: These files can not be merged:")
        for filename in sorted(conflicts):
            print(filename)
        sys.exit(1)
    
    # 执行合并 - 正确的三路合并逻辑
    merged_entries = {}
    base_entries = get_commit_entries(0) if current_commit > 0 and target_commit > 0 else {}

    # 处理所有文件
    all_files = set(current_entries.keys()) | set(target_entries.keys()) | set(base_entries.keys())

    for filename in all_files:
        current_hash = current_entries.get(filename)
        target_hash = target_entries.get(filename)
        base_hash = base_entries.get(filename)

        # 三路合并决策
        if current_hash == target_hash:
            # 两个分支相同，使用任一版本
            if current_hash:
                merged_entries[filename] = current_hash
        elif current_hash and not target_hash:
            # 只有current有，target删除了
            if current_hash != base_hash:
                # current修改了，保留current版本
                merged_entries[filename] = current_hash
            # 否则target删除，不包含在合并结果中
        elif target_hash and not current_hash:
            # 只有target有，current删除了
            if target_hash != base_hash:
                # target修改了，保留target版本
                merged_entries[filename] = target_hash
            # 否则current删除，不包含在合并结果中
        elif current_hash and target_hash:
            # 两个分支都有但不同
            if current_hash == base_hash:
                # current没变，target变了，使用target
                merged_entries[filename] = target_hash
            elif target_hash == base_hash:
                # target没变，current变了，使用current
                merged_entries[filename] = current_hash
            else:
                # 两个都变了，这种情况前面已经检查过冲突了
                # 如果到这里说明可以合并，优先使用current
                merged_entries[filename] = current_hash

    # 更新索引
    with open('.mygit/index', 'w') as f:
        for filename, file_hash in sorted(merged_entries.items()):
            f.write(f"{file_hash} {filename}\n")

    # 更新工作目录
    for filename, file_hash in merged_entries.items():
        object_path = f'.mygit/objects/{file_hash}'
        if os.path.exists(object_path):
            shutil.copy2(object_path, filename)
    
    # 创建合并提交
    commit_num = get_commit_count()
    commit_dir = f'.mygit/commits/{commit_num}'
    os.makedirs(commit_dir)
    
    # 复制索引
    shutil.copy2('.mygit/index', f'{commit_dir}/index')
    
    # 创建提交信息
    with open(f'{commit_dir}/message', 'w') as f:
        f.write(message + '\n')
    
    # 创建时间戳
    with open(f'{commit_dir}/timestamp', 'w') as f:
        f.write(str(int(time.time())) + '\n')
    
    # 更新分支
    with open(f'.mygit/branches/{current_branch}', 'w') as f:
        f.write(str(commit_num) + '\n')
    
    # 更新提交计数器
    update_commit_count(commit_num)
    
    print(f"Committed as commit {commit_num}")

def main():
    check_mygit_exists()

    if len(sys.argv) == 2:
        # 只有分支名，没有消息
        error_exit("empty commit message")
    elif len(sys.argv) == 4 and sys.argv[2] == '-m':
        target = sys.argv[1]
        message = sys.argv[3]
        merge_branch_or_commit(target, message)
    else:
        error_exit("usage: mygit-merge (branch-name|commit-number) -m message")

if __name__ == '__main__':
    main()
