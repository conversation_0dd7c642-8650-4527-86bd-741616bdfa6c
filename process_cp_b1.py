import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import matplotlib
import os

# 设置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def extract_data_from_sheet(file_path, sheet_name):
    """
    从指定工作表中提取全部数值数据
    """
    print(f"\n📊 正在提取工作表 '{sheet_name}' 的数据...")
    
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        print(f"工作表形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        all_data = []
        
        for col in df.columns:
            col_data = df[col].dropna()
            if len(col_data) > 100:  # 只处理有足够数据的列
                print(f"\n列 '{col}': {len(col_data)} 个非空值")
                print(f"前5个值: {col_data.head().tolist()}")
                
                # 检查第一个值是否为标题
                if str(col_data.iloc[0]) in ['数据', 'Data', '数值']:
                    numeric_data = pd.to_numeric(col_data.iloc[1:], errors='coerce')
                    print(f"跳过标题后: {len(numeric_data.dropna())} 个数值")
                else:
                    numeric_data = pd.to_numeric(col_data, errors='coerce')
                    print(f"数值数据: {len(numeric_data.dropna())} 个")
                
                valid_data = numeric_data.dropna()
                if len(valid_data) > 100:
                    all_data.extend(valid_data.tolist())
                    print(f"✅ 已收集 {len(valid_data)} 个数据点")
                    print(f"数据范围: {valid_data.min():.4f} 到 {valid_data.max():.4f}")
        
        if all_data:
            all_data_array = np.array(all_data)
            print(f"\n✅ 工作表 '{sheet_name}' 总共收集到 {len(all_data_array)} 个数据点")
            return all_data_array
        else:
            print(f"❌ 工作表 '{sheet_name}' 未找到有效数据")
            return None
            
    except Exception as e:
        print(f"❌ 提取工作表 '{sheet_name}' 数据时出错: {e}")
        return None

def clean_outliers(data, factor=1.5):
    """
    使用IQR方法清理异常值
    """
    Q1 = np.percentile(data, 25)
    Q3 = np.percentile(data, 75)
    IQR = Q3 - Q1
    lower_bound = Q1 - factor * IQR
    upper_bound = Q3 + factor * IQR
    cleaned_data = data[(data >= lower_bound) & (data <= upper_bound)]
    
    print(f"异常值清理: {len(data)} -> {len(cleaned_data)} 个数据点")
    print(f"移除了 {len(data) - len(cleaned_data)} 个异常值")
    
    return cleaned_data

def create_simple_normal_plot(data, sheet_name):
    """
    创建简洁的正态分布图
    """
    if data is None or len(data) == 0:
        print("❌ 没有数据可以绘图")
        return None
    
    original_count = len(data)
    
    # 清理异常值
    print(f"\n🧹 清理 {sheet_name} 的异常值...")
    data = clean_outliers(data, factor=1.5)
    
    # 计算统计信息
    mean = np.mean(data)
    std = np.std(data)
    
    print(f"\n📈 {sheet_name} 数据统计:")
    print(f"原始数据量: {original_count:,}")
    print(f"清理后数据量: {len(data):,}")
    print(f"均值: {mean:.4f}")
    print(f"标准差: {std:.4f}")
    print(f"最小值: {data.min():.4f}")
    print(f"最大值: {data.max():.4f}")
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 选择更多的bins数量，让分布更均匀平滑
    bins = min(120, max(60, int(len(data) / 300)))  # 增加bins数量

    # 绘制直方图 - 更均匀的分布
    plt.hist(data, bins=bins, density=True, alpha=0.7,
             color='lightblue', edgecolor='navy', linewidth=0.3)
    
    # 生成正态分布曲线
    x = np.linspace(data.min(), data.max(), 1000)
    y = stats.norm.pdf(x, mean, std)
    plt.plot(x, y, 'red', linewidth=3)
    
    # 设置图形属性 - 简洁版本
    plt.xlabel('数值', fontsize=14)
    plt.ylabel('频率', fontsize=14)
    plt.title('正态分布图', fontsize=18, fontweight='bold')
    plt.grid(True, alpha=0.3)

    # 在图片右上角添加工作表标识
    plt.text(0.95, 0.95, sheet_name, transform=plt.gca().transAxes,
             fontsize=16, fontweight='bold', ha='right', va='top',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    # 设置更细致的X轴刻度
    data_range = data.max() - data.min()
    if data_range > 2:
        # 如果数据范围大于2，使用0.1的间隔
        tick_interval = 0.1
    elif data_range > 1:
        # 如果数据范围在1-2之间，使用0.05的间隔
        tick_interval = 0.05
    else:
        # 如果数据范围小于1，使用0.02的间隔
        tick_interval = 0.02

    # 生成X轴刻度，限制数量避免过多
    x_min = np.floor(data.min() / tick_interval) * tick_interval
    x_max = np.ceil(data.max() / tick_interval) * tick_interval
    x_ticks = np.arange(x_min, x_max + tick_interval/2, tick_interval)

    # 限制刻度数量，避免过于密集
    if len(x_ticks) > 20:
        x_ticks = x_ticks[::2]  # 每隔一个取一个

    plt.xticks(x_ticks, rotation=45)
    plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.2f}'))
    
    # 生成输出文件名
    output_file = f'{sheet_name}_正态分布图.png'
    
    # 保存图片
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"\n✅ 图片已保存: {output_file}")
    
    # 关闭图形
    plt.close()
    
    # 正态性检验
    if len(data) <= 5000:
        shapiro_stat, shapiro_p = stats.shapiro(data[:5000])
        print(f"Shapiro-Wilk正态性检验:")
        print(f"统计量: {shapiro_stat:.4f}, p值: {shapiro_p:.4f}")
        print(f"结论: {'符合正态分布' if shapiro_p > 0.05 else '不符合正态分布'}")
    else:
        ks_stat, ks_p = stats.kstest(data, lambda x: stats.norm.cdf(x, mean, std))
        print(f"Kolmogorov-Smirnov正态性检验:")
        print(f"统计量: {ks_stat:.4f}, p值: {ks_p:.4f}")
        print(f"结论: {'符合正态分布' if ks_p > 0.05 else '不符合正态分布'}")
    
    return output_file

def main():
    """
    主函数 - 分别处理cp和B1工作表
    """
    file_path = "正太分布(2).xlsx"
    sheets = ['cp', 'B1']
    
    print("🎯 开始处理正太分布(2).xlsx文件的cp和B1工作表")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    generated_files = []
    
    for sheet_name in sheets:
        print(f"\n🔄 处理工作表: {sheet_name}")
        
        # 提取数据
        data = extract_data_from_sheet(file_path, sheet_name)
        
        if data is not None:
            # 生成正态分布图
            output_file = create_simple_normal_plot(data, sheet_name)
            if output_file:
                generated_files.append(output_file)
        else:
            print(f"❌ 工作表 {sheet_name} 数据提取失败")
    
    print("\n" + "=" * 60)
    print("🎉 所有正态分布图生成完成！")
    print(f"📁 生成的文件:")
    for file in generated_files:
        print(f"   - {file}")
    print("\n✨ 特点:")
    print("   - 标题: '正态分布图'")
    print("   - Y轴: '频率'（更直观）")
    print("   - 无虚线、无信息框、无图例")
    print("   - 已清理异常值")
    print("   - 分别使用cp和B1的全部数据")
    print("=" * 60)

if __name__ == '__main__':
    main()
