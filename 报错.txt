2041 autotest mygit
bash -c 'shopt -s nullglob; python3 -B -m py_compile mygit-*' *.py # mygit-init
bash -c 'shopt -s nullglob; check_features_used.sh mygit-*' *.py # mygit-init
bash -c 'shopt -s nullglob; check_hashbang.sh mygit-*' # mygit-init
Test subset0_1 (init) - passed
Test subset0_2 (init with existing repo) - passed
Test subset0_3 (init with existing repo) - passed
Test subset0_4 (add with no previous init) - passed
Test subset0_5 (add with non-existent file) - passed
Test subset0_6 (add file) - passed
Test subset0_7 (add many files) - passed
Test subset0_8 (add, commit, show) - passed
Test subset0_9 (show) - passed
Test subset0_10 (show errors) - passed
Test subset0_11 (add, commit, no change, commit,) - passed
Test subset0_12 (add, show, commit, show) - passed
Test subset1_13 (commit -a) - passed
Test subset1_14 (add + commit -a) - passed
Test subset1_15 (rm) - passed
Test subset1_16 (rm) - passed
Test subset1_17 (rm add) - passed
Test subset1_18 (rm errors) - passed
Test subset1_19 (rm options) - passed
Test subset1_20 (status) - passed
Test subset2_21 (branch) - passed
Test subset1_22 (rm add rm show) - passed
Test subset1_23 (add commit status change/rm) - passed
Test subset2_24 (checkout) - passed
Test subset2_25 (branch commit checkout) - passed
Test subset2_26 (checkout modified file) - failed (Incorrect output)
Your program produced these 12 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ echo world >>a
$ mygit-checkout b1
mygit-checkout: error: Your changes to the following files would be overwritten by checkout:
a
*** TEST STOPPED: incorrect output from mygit command

The correct 33 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo hello >a
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ echo world >>a
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-status
a - file changed, changes not staged for commit
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-add a
$ mygit-status
a - file changed, changes staged for commit
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-status
a - file changed, changes staged for commit
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-commit -a -m commit-B
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-status
a - same as repo
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-status
a - same as repo

The difference between your output(-) and the correct output(+) is:
...
  $ mygit-checkout b1
- mygit-checkout: error: Your changes to the following files would be overwritten by checkout:
- a
- *** TEST STOPPED: incorrect output from mygit command
+ Switched to branch 'b1'
+ $ mygit-status
+ a - file changed, changes not staged for commit
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-add a
+ $ mygit-status
+ a - file changed, changes staged for commit
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-status
+ a - file changed, changes staged for commit
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-commit -a -m commit-B
+ Committed as commit 1
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-status
+ a - same as repo
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-status
+ a - same as repo
Test subset2_27 (checkout rm) - failed (Incorrect output)
Your program produced these 10 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ touch a b c
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
*** TEST STOPPED - MISSING FILES:: these files were not present and should have been: b c

The correct 43 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ touch a b c
$ mygit-add a
$ mygit-commit -m commit-A
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ touch d e
$ mygit-rm a b
mygit-rm: error: 'b' is not in the mygit repository
$ mygit-commit -m commit-B
nothing to commit
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-branch b2
$ mygit-checkout b2
Switched to branch 'b2'
$ touch f g
$ mygit-rm b c
mygit-rm: error: 'b' is not in the mygit repository
$ mygit-add f g
$ mygit-commit -m commit-C
Committed as commit 1
$ mygit-branch
b1
b2
trunk
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout b2
Switched to branch 'b2'
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-checkout b2
Switched to branch 'b2'
$ mygit-checkout b1
Switched to branch 'b1'

The difference between your output(-) and the correct output(+) is:
...
  Switched to branch 'b1'
- *** TEST STOPPED - MISSING FILES:: these files were not present and should have been: b c
+ $ touch d e
+ $ mygit-rm a b
+ mygit-rm: error: 'b' is not in the mygit repository
+ $ mygit-commit -m commit-B
+ nothing to commit
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-branch b2
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ touch f g
+ $ mygit-rm b c
+ mygit-rm: error: 'b' is not in the mygit repository
+ $ mygit-add f g
+ $ mygit-commit -m commit-C
+ Committed as commit 1
+ $ mygit-branch
+ b1
+ b2
+ trunk
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ mygit-checkout b1
+ Switched to branch 'b1'
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-checkout b2
+ Switched to branch 'b2'
+ $ mygit-checkout b1
+ Switched to branch 'b1'
Test subset2_28 (delete branch with unmerged work) - passed
Test subset2_29 (checkout with work that would be over-written) - passed
Test subset2_30 (successful merge) - passed
Test subset2_31 (successful merge - multiple files) - failed (Incorrect output)
Your program produced these 46 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq -f "line %.0f" 1 7 >a
$ seq -f "line %.0f" 1 7 >b
$ seq -f "line %.0f" 1 7 >c
$ seq -f "line %.0f" 1 7 >d
$ mygit-add a b c d
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ seq -f "line %.0f" 0 7 >a
$ seq -f "line %.0f" 1 8 >b
$ seq -f "line %.0f" 1 7 >e
$ mygit-add e
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -i 4d c
$ seq -f "line %.0f" 0 8 >d
$ seq -f "line %.0f" 1 7 >f
$ mygit-add f
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge1
Committed as commit 3
*** TEST STOPPED - INCORECT FILE: file 'c' has incorrect contents
*** 'c' contents are:
line 1
line 2
line 3
line 4
line 5
line 6
line 7

*** 'c' contents should be:
line 1
line 2
line 3
line 5
line 6
line 7


The correct 40 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq -f "line %.0f" 1 7 >a
$ seq -f "line %.0f" 1 7 >b
$ seq -f "line %.0f" 1 7 >c
$ seq -f "line %.0f" 1 7 >d
$ mygit-add a b c d
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ seq -f "line %.0f" 0 7 >a
$ seq -f "line %.0f" 1 8 >b
$ seq -f "line %.0f" 1 7 >e
$ mygit-add e
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -i 4d c
$ seq -f "line %.0f" 0 8 >d
$ seq -f "line %.0f" 1 7 >f
$ mygit-add f
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge1
Committed as commit 3
$ mygit-log
3 merge1
2 commit-2
1 commit-1
0 commit-0
$ mygit-status
a - same as repo
b - same as repo
c - same as repo
d - same as repo
e - same as repo
f - same as repo

The difference between your output(-) and the correct output(+) is:
...
  Committed as commit 3
+ $ mygit-log
+ 3 merge1
+ 2 commit-2
+ 1 commit-1
+ 0 commit-0
+ $ mygit-status
+ a - same as repo
+ b - same as repo
+ c - same as repo
+ d - same as repo
+ e - same as repo
+ f - same as repo
- *** TEST STOPPED - INCORECT FILE: file 'c' has incorrect contents
- *** 'c' contents are:
- line 1
- line 2
- line 3
- line 4
- line 5
- line 6
- line 7
- 
- *** 'c' contents should be:
- line 1
- line 2
- line 3
- line 5
- line 6
- line 7
- 
Test subset2_32 (merge conflict) - failed (Incorrect output)
Your program produced these 25 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq 1 7 >7.txt
$ mygit-add 7.txt
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ sed -Ei s/2/42/ 7.txt
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -Ei s/5/24/ 7.txt
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge-message
mygit-merge: error: These files can not be merged:
7.txt
$ mygit-log
2 commit-2
1 commit-1
0 commit-0
*** TEST STOPPED: incorrect output from mygit command

The correct 25 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ seq 1 7 >7.txt
$ mygit-add 7.txt
$ mygit-commit -m commit-0
Committed as commit 0
$ mygit-branch b1
$ mygit-checkout b1
Switched to branch 'b1'
$ sed -Ei s/2/42/ 7.txt
$ mygit-commit -a -m commit-1
Committed as commit 1
$ mygit-checkout trunk
Switched to branch 'trunk'
$ sed -Ei s/5/24/ 7.txt
$ mygit-commit -a -m commit-2
Committed as commit 2
$ mygit-merge b1 -m merge-message
mygit-merge: error: These files can not be merged:
7.txt
$ mygit-log
2 commit-2
0 commit-0
$ mygit-status
7.txt - same as repo

The difference between your output(-) and the correct output(+) is:
...
  2 commit-2
- 1 commit-1
  0 commit-0
- *** TEST STOPPED: incorrect output from mygit command
+ $ mygit-status
+ 7.txt - same as repo
Test subset2_33 (merge errors) - passed
Test subset2_34 (many branches) - failed (Incorrect output)
Your program produced these 67 lines of output:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo 0 >level0
$ mygit-add level0
$ mygit-commit -m root
Committed as commit 0
$ mygit-branch b0
$ mygit-branch b1
$ mygit-checkout b0
Switched to branch 'b0'
$ echo 0 >level1
$ mygit-add level1
$ mygit-commit -m 0
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
$ echo 1 >level1
$ mygit-add level1
$ mygit-commit -m 1
Committed as commit 2
$ mygit-checkout b0
Switched to branch 'b0'
$ mygit-branch b00
$ mygit-branch b01
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-branch b10
$ mygit-branch b11
$ mygit-checkout b00
Switched to branch 'b00'
$ echo 00 >level2
$ mygit-add level2
$ mygit-commit -m 00
Committed as commit 3
$ mygit-checkout b01
Switched to branch 'b01'
$ echo 01 >level2
$ mygit-add level2
$ mygit-commit -m 01
Committed as commit 4
$ mygit-checkout b10
Switched to branch 'b10'
$ echo 10 >level2
$ mygit-add level2
$ mygit-commit -m 10
Committed as commit 5
$ mygit-checkout b11
Switched to branch 'b11'
$ echo 11 >level2
$ mygit-add level2
$ mygit-commit -m 11
Committed as commit 6
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-log
0 root
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-log
2 1
0 root
$ mygit-checkout b01
Switched to branch 'b01'
$ mygit-log
4 01
0 root
*** TEST STOPPED: incorrect output from mygit command

The correct 83 lines of output for this test were:
$ mygit-init
Initialized empty mygit repository in .mygit
$ echo 0 >level0
$ mygit-add level0
$ mygit-commit -m root
Committed as commit 0
$ mygit-branch b0
$ mygit-branch b1
$ mygit-checkout b0
Switched to branch 'b0'
$ echo 0 >level1
$ mygit-add level1
$ mygit-commit -m 0
Committed as commit 1
$ mygit-checkout b1
Switched to branch 'b1'
$ echo 1 >level1
$ mygit-add level1
$ mygit-commit -m 1
Committed as commit 2
$ mygit-checkout b0
Switched to branch 'b0'
$ mygit-branch b00
$ mygit-branch b01
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-branch b10
$ mygit-branch b11
$ mygit-checkout b00
Switched to branch 'b00'
$ echo 00 >level2
$ mygit-add level2
$ mygit-commit -m 00
Committed as commit 3
$ mygit-checkout b01
Switched to branch 'b01'
$ echo 01 >level2
$ mygit-add level2
$ mygit-commit -m 01
Committed as commit 4
$ mygit-checkout b10
Switched to branch 'b10'
$ echo 10 >level2
$ mygit-add level2
$ mygit-commit -m 10
Committed as commit 5
$ mygit-checkout b11
Switched to branch 'b11'
$ echo 11 >level2
$ mygit-add level2
$ mygit-commit -m 11
Committed as commit 6
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-log
0 root
$ mygit-checkout b1
Switched to branch 'b1'
$ mygit-log
2 1
0 root
$ mygit-checkout b01
Switched to branch 'b01'
$ mygit-log
4 01
1 0
0 root
$ mygit-checkout b11
Switched to branch 'b11'
$ mygit-log
6 11
2 1
0 root
$ mygit-checkout trunk
Switched to branch 'trunk'
$ mygit-merge b0 -m merge0
Fast-forward: no commit created
$ mygit-merge b00 -m merge00
Fast-forward: no commit created
$ mygit-log
3 00
1 0
0 root

The difference between your output(-) and the correct output(+) is:
...
  4 01
+ 1 0
  0 root
- *** TEST STOPPED: incorrect output from mygit command
+ $ mygit-checkout b11
+ Switched to branch 'b11'
+ $ mygit-log
+ 6 11
+ 2 1
+ 0 root
+ $ mygit-checkout trunk
+ Switched to branch 'trunk'
+ $ mygit-merge b0 -m merge0
+ Fast-forward: no commit created
+ $ mygit-merge b00 -m merge00
+ Fast-forward: no commit created
+ $ mygit-log
+ 3 00
+ 1 0
+ 0 root
29 tests passed 5 tests failed