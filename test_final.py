"""
测试最终版本的Q1和Q2实现
"""

def test_q1():
    """测试Q1实现"""
    print("🧪 测试Q1: Top-k最短简单路径")
    print("=" * 50)
    
    try:
        # 导入Q1实现
        import sys
        sys.path.append('.')
        
        from Q1 import KShortestPathsQuery
        
        # 简单测试图
        G = {
            's': [('a', 1), ('t', 10)],
            'a': [('t', 2)],
            't': []
        }
        
        query = KShortestPathsQuery()
        
        print("测试图: s->a->t (权重3), s->t (权重10)")
        
        # 测试k=1
        result1 = query.query(G, 's', 't', 1)
        print(f"k=1: {result1}")
        
        # 测试k=2
        result2 = query.query(G, 's', 't', 2)
        print(f"k=2: {result2}")
        
        # 验证结果
        if result1 == [['s', 'a', 't']]:
            print("✅ k=1测试通过")
        else:
            print(f"❌ k=1测试失败，期望[['s', 'a', 't']]，得到{result1}")
            
        if len(result2) == 2:
            print("✅ k=2测试通过")
        else:
            print(f"❌ k=2测试失败，期望2条路径，得到{len(result2)}条")
        
        print("✅ Q1基本功能正常")
        
    except Exception as e:
        print(f"❌ Q1测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_q2():
    """测试Q2实现"""
    print("\n🧪 测试Q2: k-core结构多样性")
    print("=" * 50)
    
    try:
        from Q2_corrected import kCoreBaseStructuralDiversity
        
        # 创建模拟图类
        class MockGraph:
            def __init__(self, edges):
                self._nodes = set()
                self._adj = {}
                
                for u, v in edges:
                    self._nodes.add(u)
                    self._nodes.add(v)
                    
                    if u not in self._adj:
                        self._adj[u] = set()
                    if v not in self._adj:
                        self._adj[v] = set()
                    
                    self._adj[u].add(v)
                    self._adj[v].add(u)
            
            def nodes(self):
                return list(self._nodes)
            
            def neighbors(self, v):
                return list(self._adj.get(v, []))
        
        # 简单测试图
        edges = [(1, 2), (2, 3), (3, 1), (3, 4)]
        G = MockGraph(edges)
        
        print("测试图: 1-2-3-1 (三角形) + 3-4")
        
        # 测试k=1
        result1 = kCoreBaseStructuralDiversity.process(G, 1)
        print(f"k=1结果: {result1}")
        
        # 测试k=2  
        result2 = kCoreBaseStructuralDiversity.process(G, 2)
        print(f"k=2结果: {result2}")
        
        if len(result1) == 4:  # 4个顶点
            print("✅ k=1测试通过")
        else:
            print(f"❌ k=1测试失败，期望4个结果，得到{len(result1)}个")
            
        if len(result2) == 4:  # 4个顶点
            print("✅ k=2测试通过")
        else:
            print(f"❌ k=2测试失败，期望4个结果，得到{len(result2)}个")
        
        print("✅ Q2基本功能正常")
        
    except Exception as e:
        print(f"❌ Q2测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_requirements():
    """检查是否符合HTML要求"""
    print("\n🔍 检查HTML要求符合性")
    print("=" * 50)
    
    # 检查Q1
    print("Q1要求检查:")
    try:
        from Q1 import KShortestPathsQuery
        
        # 检查类名
        if hasattr(KShortestPathsQuery, 'query'):
            print("✅ 类名KShortestPathsQuery正确")
            print("✅ 方法query存在")
        else:
            print("❌ 方法query不存在")
            
        # 检查是否可以使用标准库
        import heapq, collections
        print("✅ 可以使用标准库 (heapq, collections)")
        
    except Exception as e:
        print(f"❌ Q1要求检查失败: {e}")
    
    # 检查Q2
    print("\nQ2要求检查:")
    try:
        from Q2_corrected import kCoreBaseStructuralDiversity
        
        # 检查类名
        if hasattr(kCoreBaseStructuralDiversity, 'process'):
            print("✅ 类名kCoreBaseStructuralDiversity正确")
            print("✅ 方法process存在")
        else:
            print("❌ 方法process不存在")
            
        # 检查是否没有import语句
        with open('Q2_corrected.py', 'r') as f:
            content = f.read()
            if 'import ' not in content:
                print("✅ 没有使用任何import语句")
            else:
                print("❌ 发现import语句")
                
    except Exception as e:
        print(f"❌ Q2要求检查失败: {e}")

def main():
    """运行所有测试"""
    print("🚀 最终版本测试")
    print("=" * 60)
    
    test_q1()
    test_q2()
    check_requirements()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

if __name__ == "__main__":
    main()
