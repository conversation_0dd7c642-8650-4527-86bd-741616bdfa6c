#!/usr/bin/env python3
import os
import sys
def main():
    if os.path.exists('.mygit'):
        print("mygit-init: error: .mygit already exists", file=sys.stderr)
        sys.exit(1)
    try:
        os.makedirs('.mygit')
        os.makedirs('.mygit/objects')
        os.makedirs('.mygit/commits')
        os.makedirs('.mygit/branches')
        with open('.mygit/index', 'w') as f:
            pass
        with open('.mygit/HEAD', 'w') as f:
            f.write('trunk\n')
        with open('.mygit/branches/trunk', 'w') as f:
            f.write('-1\n')
        with open('.mygit/commit_count', 'w') as f:
            f.write('0\n')
        print("Initialized empty mygit repository in .mygit")
    except OSError as e:
        print(f"mygit-init: error: cannot create .mygit directory: {e}", file=sys.stderr)
        sys.exit(1)
if __name__ == '__main__':
    main()
