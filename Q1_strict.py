import heapq
from array import array

class KShortestPathsQuery(object):
    def __init__(self):
        pass

    @staticmethod
    def query(G, s, t, k):
        if s == t:
            return [[s]] if k > 0 else []

        # 预计算邻接表和边权重
        adj_out = [[] for _ in range(G.vertex_num)]
        edge_weights = {}
        for u in range(G.vertex_num):
            for v, w in G.adj_list_out[u]:
                adj_out[u].append((v, w))
                edge_weights[(u, v)] = w

        # 找到第一条最短路径
        dist, prev = KShortestPathsQuery._dijkstra(adj_out, s, t, G.vertex_num)
        if dist[t] == float('inf'):
            return []

        # 重建第一条路径
        first_path = []
        curr = t
        while curr != -1:
            first_path.append(curr)
            curr = prev[curr]
        first_path.reverse()

        if k == 1:
            return [first_path]

        # Yen算法主体 - 严格按照标准实现
        A = [first_path]
        B = []
        
        while len(A) < k:
            # 对最近添加的路径生成候选路径
            prev_path = A[-1]
            
            for i in range(len(prev_path) - 1):
                spur_node = prev_path[i]
                root_path = prev_path[:i+1]

                # 收集需要排除的边
                excluded_edges = set()
                for path in A:
                    if len(path) > i + 1 and path[:i+1] == root_path:
                        excluded_edges.add((path[i], path[i+1]))

                # 排除根路径中的节点
                excluded_nodes = set(root_path[:-1])

                # 计算偏离路径
                spur_dist, spur_prev = KShortestPathsQuery._dijkstra_excluded(
                    adj_out, spur_node, t, excluded_nodes, excluded_edges, G.vertex_num
                )

                if spur_dist[t] != float('inf'):
                    # 重建偏离路径
                    spur_path = []
                    curr = t
                    while curr != -1:
                        spur_path.append(curr)
                        if curr == spur_node:
                            break
                        curr = spur_prev[curr]
                    spur_path.reverse()

                    # 合并路径
                    candidate = root_path[:-1] + spur_path

                    # 计算权重
                    weight = 0
                    for j in range(len(candidate) - 1):
                        weight += edge_weights[(candidate[j], candidate[j+1])]

                    # 检查是否已存在
                    candidate_tuple = tuple(candidate)
                    already_exists = False
                    for existing_path in A:
                        if tuple(existing_path) == candidate_tuple:
                            already_exists = True
                            break
                    
                    if not already_exists:
                        # 检查是否已在候选列表中
                        in_candidates = False
                        for _, existing_candidate in B:
                            if existing_candidate == candidate_tuple:
                                in_candidates = True
                                break
                        
                        if not in_candidates:
                            # 严格的字典序排序
                            heapq.heappush(B, (weight, candidate_tuple))

            if not B:
                break

            # 选择下一条最短路径
            weight, next_path_tuple = heapq.heappop(B)
            A.append(list(next_path_tuple))

        return A
    
    @staticmethod
    def _dijkstra(adj_list, start, target, n):
        """高效Dijkstra算法"""
        dist = array('d', [float('inf')] * n)
        dist[start] = 0
        prev = array('i', [-1] * n)
        visited = [False] * n
        pq = [(0, start)]

        while pq:
            d, u = heapq.heappop(pq)
            
            if u == target:
                break
                
            if visited[u]:
                continue
                
            visited[u] = True
            
            for v, w in adj_list[u]:
                if not visited[v]:
                    new_dist = dist[u] + w
                    if new_dist < dist[v]:
                        dist[v] = new_dist
                        prev[v] = u
                        heapq.heappush(pq, (new_dist, v))

        return dist, prev

    @staticmethod
    def _dijkstra_excluded(adj_list, start, target, excluded_nodes, excluded_edges, n):
        """带排除条件的Dijkstra"""
        dist = array('d', [float('inf')] * n)
        dist[start] = 0
        prev = array('i', [-1] * n)
        visited = [False] * n
        
        # 排除节点
        for node in excluded_nodes:
            if node != target:
                visited[node] = True
        
        pq = [(0, start)]
        
        while pq:
            d, u = heapq.heappop(pq)
            
            if u == target:
                break
                
            if visited[u]:
                continue
                
            visited[u] = True
            
            for v, w in adj_list[u]:
                if (u, v) in excluded_edges or visited[v]:
                    continue
                    
                new_dist = dist[u] + w
                if new_dist < dist[v]:
                    dist[v] = new_dist
                    prev[v] = u
                    heapq.heappush(pq, (new_dist, v))
        
        return dist, prev
