
import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
import math

from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
from timm.models.vision_transformer import _cfg
from einops import rearrange
import zeta
from zeta.nn import SSM  # 确保已安装zeta库

# ====================== 从PVT.py中复制的注意力机制组件 ======================
class BasicConv(nn.Module):
    def __init__(
            self,
            in_planes,
            out_planes,
            kernel_size,
            stride=1,
            padding=0,
            dilation=1,
            groups=1,
            relu=True,
            bn=True,
            bias=False,
    ):
        super(BasicConv, self).__init__()
        self.out_channels = out_planes
        self.conv = nn.Conv2d(
            in_planes,
            out_planes,
            kernel_size=kernel_size,
            stride=stride,
            padding=padding,
            dilation=dilation,
            groups=groups,
            bias=bias,
        )
        self.bn = (
            nn.BatchNorm2d(out_planes, eps=1e-5, momentum=0.01, affine=True)
            if bn
            else None
        )
        self.relu = nn.ReLU() if relu else None

    def forward(self, x):
        x = self.conv(x)
        if self.bn is not None:
            x = self.bn(x)
        if self.relu is not None:
            x = self.relu(x)
        return x

class ZPool(nn.Module):
    def forward(self, x):
        return torch.cat(
            (torch.max(x, 1)[0].unsqueeze(1), torch.mean(x, 1).unsqueeze(1)), dim=1
        )

class AttentionGate(nn.Module):
    def __init__(self):
        super(AttentionGate, self).__init__()
        kernel_size = 7
        self.compress = ZPool()
        self.conv = BasicConv(
            2, 1, kernel_size, stride=1, padding=(kernel_size - 1) // 2, relu=False
        )

    def forward(self, x):
        x_compress = self.compress(x)
        x_out = self.conv(x_compress)
        scale = torch.sigmoid_(x_out)
        return x * scale

class TripletAttention(nn.Module):
    def __init__(self, no_spatial=False):
        super(TripletAttention, self).__init__()
        self.cw = AttentionGate()
        self.hc = AttentionGate()

    def forward(self, x):
        x_perm1 = x.permute(0, 2, 1, 3).contiguous()
        x_out1 = self.cw(x_perm1)
        x_out11 = x_out1.permute(0, 2, 1, 3).contiguous()
        x_perm2 = x.permute(0, 3, 2, 1).contiguous()
        x_out2 = self.hc(x_perm2)
        x_out21 = x_out2.permute(0, 3, 2, 1).contiguous()
        x_out = 1 / 2 * (x_out11 + x_out21)
        return x_out

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=3):
        super(SpatialAttention, self).__init__()
        kernel_size = 7
        padding = kernel_size // 2
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        concat = torch.cat([avg_out, max_out], dim=1)
        att_map = self.sigmoid(self.conv(concat))
        return x * att_map

class SKConv(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1, M=2, r=16, L=32, groups=16):
        super(SKConv, self).__init__()
        self.M = M
        self.out_channels = out_channels
        d = max(in_channels // r, L)
        self.convs = nn.ModuleList()
        for i in range(M):
            if i == 1:
                kernel_size = 3
                dilation = 2
                padding = dilation
                conv_branch = nn.Sequential(
                    nn.Conv2d(in_channels, out_channels, kernel_size,
                              stride=stride, padding=padding, dilation=dilation,
                              groups=groups, bias=False),
                    nn.BatchNorm2d(out_channels),
                    nn.ReLU(inplace=False)
                )
            else:
                kernel_size = 3 + i * 2
                padding = (kernel_size - 1) // 2
                conv_branch = nn.Sequential(
                    nn.Conv2d(in_channels, out_channels, kernel_size,
                              stride=stride, padding=padding, groups=groups, bias=False),
                    nn.BatchNorm2d(out_channels),
                    nn.ReLU(inplace=False)
                )
            self.convs.append(conv_branch)
        self.gap = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Conv2d(out_channels, d, kernel_size=1, bias=False),
            nn.BatchNorm2d(d),
            nn.ReLU(inplace=False)
        )
        self.fcs = nn.ModuleList([
            nn.Conv2d(d, out_channels, kernel_size=1, bias=False)
            for _ in range(M)
        ])
        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        branch_outputs = []
        for conv_branch in self.convs:
            branch_out = conv_branch(x)
            branch_outputs.append(branch_out)
        feats = torch.stack(branch_outputs, dim=1)
        U = torch.sum(feats, dim=1)
        S = self.gap(U)
        Z = self.fc(S)
        weights = []
        for fc in self.fcs:
            weight = fc(Z)
            weights.append(weight)
        weights = torch.stack(weights, dim=1)
        weights = self.softmax(weights)
        weighted_feats = feats * weights
        V = torch.sum(weighted_feats, dim=1)
        return V
# ====================== mamba的块（Github复制下来的） ======================
class VisionMambaBlock(nn.Module):
    """Mamba块，用于并行处理路径"""
    def __init__(self, dim, dt_rank, dim_inner, d_state):
        super().__init__()
        self.dim = dim
        self.dt_rank = dt_rank
        self.dim_inner = dim_inner
        self.d_state = d_state

        self.in_proj = nn.Linear(dim, dim_inner)
        self.out_proj = nn.Linear(dim_inner, dim)

        self.forward_conv1d = nn.Conv1d(dim, dim, kernel_size=1)
        self.backward_conv1d = nn.Conv1d(dim, dim, kernel_size=1)
        self.norm = nn.LayerNorm(dim)
        self.silu = nn.SiLU()
        self.ssm = SSM(dim, dt_rank, dim_inner, d_state)
        self.proj = nn.Linear(dim, dim)
        self.softplus = nn.Softplus()

    def process_direction(self, x, conv1d, ssm):
        x = rearrange(x, "b s d -> b d s")
        x = self.softplus(conv1d(x))
        x = rearrange(x, "b d s -> b s d")
        return ssm(x)

    def forward(self, x):
        skip = x
        x = self.norm(x)
        z1 = self.proj(x)
        x = self.proj(x)

        # 添加投影操作
        x = self.in_proj(x)
        x1 = self.process_direction(x, self.forward_conv1d, self.ssm)
        x2 = self.process_direction(x, self.backward_conv1d, self.ssm)
        x = x1 + x2
        x = self.out_proj(x)  # 投影回原始维度

        z = self.silu(z1)
        return x * z + skip



class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class Attention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0., sr_ratio=1):
        super().__init__()
        assert dim % num_heads == 0, f"dim {dim} should be divided by num_heads {num_heads}."

        self.dim = dim
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        self.sr_ratio = sr_ratio
        if sr_ratio > 1:
            self.sr = nn.Conv2d(dim, dim, kernel_size=sr_ratio, stride=sr_ratio)
            self.norm = nn.LayerNorm(dim)

    def forward(self, x, H, W):
        B, N, C = x.shape
        q = self.q(x).reshape(B, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)

        if self.sr_ratio > 1:
            x_ = x.permute(0, 2, 1).reshape(B, C, H, W)
            x_ = self.sr(x_).reshape(B, C, -1).permute(0, 2, 1)
            x_ = self.norm(x_)
            kv = self.kv(x_).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        else:
            kv = self.kv(x).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        return x


class Block(nn.Module):

    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, sr_ratio=1):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.attn = Attention(
            dim,
            num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale,
            attn_drop=attn_drop, proj_drop=drop, sr_ratio=sr_ratio)
        # NOTE: drop path for stochastic depth, we shall see if this is better than dropout here
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

    def forward(self, x, H, W):
        x = x + self.drop_path(self.attn(self.norm1(x), H, W))
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x


class PatchEmbed(nn.Module):
    """ Image to Patch Embedding
    """

    def __init__(self, img_size=224, patch_size=16, in_chans=3, embed_dim=768):
        super().__init__()
        img_size = to_2tuple(img_size)
        patch_size = to_2tuple(patch_size)

        self.img_size = img_size
        self.patch_size = patch_size
        # assert img_size[0] % patch_size[0] == 0 and img_size[1] % patch_size[1] == 0, \
        #     f"img_size {img_size} should be divided by patch_size {patch_size}."
        self.H, self.W = img_size[0] // patch_size[0], img_size[1] // patch_size[1]
        self.num_patches = self.H * self.W
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
        self.norm = nn.LayerNorm(embed_dim)

    def forward(self, x):
        B, C, H, W = x.shape

        x = self.proj(x).flatten(2).transpose(1, 2)
        x = self.norm(x)
        H, W = H // self.patch_size[0], W // self.patch_size[1]

        return x, (H, W)

# =============PVT分支和Mamba分支======================
class PyramidVisionTransformer(nn.Module):
    def __init__(self, img_size=224, patch_size=16, in_chans=3, num_classes=1000, embed_dims=[64, 128, 256, 512],
                 num_heads=[1, 2, 4, 8], mlp_ratios=[4, 4, 4, 4], qkv_bias=False, qk_scale=None, drop_rate=0.,
                 attn_drop_rate=0., drop_path_rate=0., norm_layer=nn.LayerNorm,
                 depths=[3, 4, 6, 3], sr_ratios=[8, 4, 2, 1], num_stages=4, seg=False ,dt_rank=32, d_state=16):
        super().__init__()
        self.num_classes = num_classes
        self.depths = depths
        self.num_stages = num_stages
        self.seg = seg
        # ========== Mamba分支 ==========
        self.mamba_dims = embed_dims.copy()
        self.mamba_patch_embed = nn.Sequential(
            nn.Conv2d(in_chans, self.mamba_dims[0], kernel_size=patch_size, stride=patch_size),
            Rearrange('b c h w -> b (h w) c')
        )
        self.mamba_stages = nn.ModuleList()
        for i in range(num_stages):
            stage = nn.Sequential(
                *[VisionMambaBlock(
                    dim=self.mamba_dims[i],
                    dt_rank=dt_rank,
                    dim_inner=self.mamba_dims[i] * 2,
                    d_state=d_state,
                ) for _ in range(depths[i])],
                nn.Sequential(
                    Rearrange('b (h w) c -> b c h w', h=img_size // (patch_size * (2 ** i))),
                    nn.Conv2d(
                        self.mamba_dims[i],
                        self.mamba_dims[i + 1] if i < num_stages - 1 else self.mamba_dims[i],
                        kernel_size=3, stride=2, padding=1
                    ) if i < num_stages - 1 else nn.Identity(),
                    Rearrange('b c h w -> b (h w) c'),
                ) if i < num_stages - 1 else nn.Identity()
            )
            self.mamba_stages.append(stage)
        # 最终特征融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(embed_dims[-1] + self.mamba_dims[-1], 16),
            nn.LayerNorm(16),
            nn.GELU()
        )

        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]  # stochastic depth decay rule
        cur = 0

        for i in range(num_stages):
            patch_embed = PatchEmbed(img_size=img_size if i == 0 else img_size // (2 ** (i + 1)),
                                     patch_size=patch_size if i == 0 else 2,
                                     in_chans=in_chans if i == 0 else embed_dims[i - 1],
                                     embed_dim=embed_dims[i])
            num_patches = patch_embed.num_patches if i != num_stages - 1 else patch_embed.num_patches + 1
            pos_embed = nn.Parameter(torch.zeros(1, num_patches, embed_dims[i]))
            pos_drop = nn.Dropout(p=drop_rate)

            block = nn.ModuleList([Block(
                dim=embed_dims[i], num_heads=num_heads[i], mlp_ratio=mlp_ratios[i], qkv_bias=qkv_bias,
                qk_scale=qk_scale, drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[cur + j],
                norm_layer=norm_layer, sr_ratio=sr_ratios[i])
                for j in range(depths[i])])
            cur += depths[i]

            setattr(self, f"patch_embed{i + 1}", patch_embed)
            setattr(self, f"pos_embed{i + 1}", pos_embed)
            setattr(self, f"pos_drop{i + 1}", pos_drop)
            setattr(self, f"block{i + 1}", block)

        self.norm = norm_layer(embed_dims[3])

        # cls_token
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dims[3]))

        # classification head
        self.head = nn.Linear(embed_dims[3], num_classes) if num_classes > 0 else nn.Identity()

        # init weights
        for i in range(num_stages):
            pos_embed = getattr(self, f"pos_embed{i + 1}")
            trunc_normal_(pos_embed, std=.02)
        trunc_normal_(self.cls_token, std=.02)
        self.apply(self._init_weights)
        self.forward = self.forward_seg if self.seg else self.forward_cls_

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def _get_pos_embed(self, pos_embed, patch_embed, H, W):
        if H * W == self.patch_embed1.num_patches:
            return pos_embed
        else:
            return F.interpolate(
                pos_embed.reshape(1, patch_embed.H, patch_embed.W, -1).permute(0, 3, 1, 2),
                size=(H, W), mode="bilinear").reshape(1, -1, H * W).permute(0, 2, 1)

    def forward_features(self, x):
        B = x.shape[0]
        pvt_features = []
        mamba_features = []
        x_pvt = x
        x_mamba = self.mamba_patch_embed(x)

        for i in range(self.num_stages):
            # PVT分支
            patch_embed = getattr(self, f"patch_embed{i + 1}")
            pos_embed = getattr(self, f"pos_embed{i + 1}")
            pos_drop = getattr(self, f"pos_drop{i + 1}")
            block = getattr(self, f"block{i + 1}")

            x_pvt ,(H, W) = patch_embed(x_pvt)

            pos_embed = self._get_pos_embed(pos_embed, patch_embed, H, W)
            x_pvt = pos_drop(x_pvt + pos_embed)

            for blk in block:
                x_pvt = blk(x_pvt, H, W)

            # Mamba分支
            x_mamba = self.mamba_stages[i](x_mamba)
            # 计算当前Mamba特征图尺寸
            mamba_H = mamba_W = self.img_size // (self.patch_size * (2 ** i))

            # 保存特征

            # PVT分支的保存
            pvt_spatial = x_pvt.reshape(B, H, W, -1).permute(0, 3, 1, 2).contiguous()
            pvt_features.append(pvt_spatial)
            # Mamba分支
            mamba_spatial = x_mamba.reshape(B, mamba_H, mamba_W, -1).permute(0, 3, 1, 2).contiguous()
            mamba_features.append(mamba_spatial)

            if i < self.num_stages - 1:
                x_pvt = pvt_spatial  # 准备下一阶段输入

        # ========== 最终特征融合 ==========
        # 最后阶段的特征图尺寸
        final_H = self.img_size // (self.patch_size * (2 ** (self.num_stages - 1)))
        final_W = final_H



        # 处理PVT分支最后输出
        pvt_final = pvt_features[-1].flatten(2).permute(0, 2, 1)  # [B, C, H, W] -> [B, N, C]

        # 处理Mamba分支最后输出
        mamba_final = x_mamba  # 已经是[B, N, C]格式

        # 特征融合
        combined = torch.cat([pvt_final, mamba_final], dim=-1)
        fused_feature = self.final_fusion(combined)

        # 重塑为空间格式
        fused_feature = fused_feature.reshape(B, final_H, final_W, -1).permute(0, 3, 1, 2).contiguous()

        return pvt_features, mamba_features, fused_feature

    def forward_cls(self, x):
        _, _, fused_feature = self.forward_features(x)
        fused_feature = fused_feature.mean(dim=[2, 3])
        return self.head(fused_feature)

    def forward_seg(self, x):
        pvt_features, mamba_features, fused_feature = self.forward_features(x)
        # 返回PVT特征、Mamba特征和融合特征
        return pvt_features, mamba_features, fused_feature

class Rearrange(nn.Module):
    def __init__(self, pattern, **axes_lengths):
        super().__init__()
        self.pattern = pattern
        self.axes_lengths = axes_lengths

    def forward(self, x):
        return rearrange(x, self.pattern, **self.axes_lengths)


def _conv_filter(state_dict, patch_size=16):
    """ convert patch embedding weight from manual patchify + linear proj to conv"""
    out_dict = {}
    for k, v in state_dict.items():
        if 'patch_embed.proj.weight' in k:
            v = v.reshape((v.shape[0], 3, patch_size, patch_size))
        out_dict[k] = v

    return out_dict


class ConvBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(ConvBlock, self).__init__()
        self.block = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.block(x)


class DecoderBlock(nn.Module):
    def __init__(self, in_channels, skip_channels, out_channels):
        super(DecoderBlock, self).__init__()
        self.up = nn.ConvTranspose2d(in_channels, out_channels, kernel_size=2, stride=2)
        self.conv = ConvBlock(out_channels + skip_channels, out_channels)

    def forward(self, x, skip):
        x = self.up(x)

        if skip is not None:
            x = torch.cat([x, skip], dim=1)

        x = self.conv(x)

        return x


class FCNHead(nn.Module):
    def __init__(self, in_channels, num_classes):
        super(FCNHead, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, 8, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(8)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(8, num_classes, kernel_size=1)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.conv2(x)
        return x


class PVT_UNet(nn.Module):
    def __init__(self, num_classes=1):
        super(PVT_UNet, self).__init__()
        self.encoder = PyramidVisionTransformer(in_chans=3, sr_ratios=[2, 2, 1, 1], patch_size=2,
                                                embed_dims=[8, 8, 8, 8], num_heads=[1, 1, 2, 2],
                                                mlp_ratios=[2, 2, 1, 1], qkv_bias=True,
                                                norm_layer=partial(nn.LayerNorm, eps=1e-6), depths=[1, 1, 1, 1],
                                                seg=True, dt_rank=32, d_state=16)

        # PVT returns multi-scale features
        encoder_channels = [8, 16, 16, 16]

        # Decoder path
        self.decoder4 = DecoderBlock(16, 16, 16)
        self.decoder3 = DecoderBlock(16, 16, 16)
        self.decoder2 = DecoderBlock(16, 8, 8)
        self.decoder1 = nn.ConvTranspose2d(8, 8, kernel_size=2, stride=2)

        # Final classifier
        self.final_conv = nn.Conv2d(8, num_classes, kernel_size=1)

    def forward(self, x):
        H, W = x.shape[2], x.shape[3]
        print(f"Input shape: {x.shape}")
        # 获取编码器特征
        pvt_features, fused_feature = self.encoder(x)

        x1 = pvt_features[0]  # Stage 1
        x2 = pvt_features[1]  # Stage 2
        x3 = pvt_features[2]  # Stage 3

        # 使用融合特征作为最深层的输入
        x4 = fused_feature  # (16通道)

        d4 = self.decoder4(x4, x3)
        d3 = self.decoder3(d4, x2)
        d2 = self.decoder2(d3, x1)
        d1 = self.decoder1(d2)

        out = self.final_conv(d1)

        out = F.interpolate(out, size=(H, W), mode='bilinear', align_corners=False)
        return out


if __name__ == '__main__':
    model = PVT_UNet(num_classes=2)  # Change num_classes as needed
    x = torch.randn(1, 3, 256, 256)  # Example input
    y = model(x)
    print(f'Output shape: {y.shape}')

