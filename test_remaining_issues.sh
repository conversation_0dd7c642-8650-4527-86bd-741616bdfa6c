#!/usr/bin/env dash

echo "=== Testing remaining issues ==="

# Create clean test directory
mkdir -p test_remaining
cd test_remaining

echo "1. Testing subset2_32 (merge conflict log):"
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1
python3 ../mygit-checkout trunk
sed -Ei 's/5/24/' 7.txt
python3 ../mygit-commit -a -m commit-2
python3 ../mygit-merge b1 -m merge-message
echo "Log after failed merge:"
python3 ../mygit-log

echo ""
echo "2. Testing subset2_26 (checkout modified file):"
rm -rf .mygit *
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch b1
echo world >> a
echo "File content before checkout:"
cat a
python3 ../mygit-checkout b1
echo "File content after checkout:"
cat a

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_remaining
