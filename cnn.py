#!/usr/bin/env python
# coding: utf-8

# In[1]:


import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
from tensorflow.keras.models import Model
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv1D, LSTM, Dense,Dropout,Input,MaxPooling1D,Bidirectional
from sklearn.metrics import mean_squared_error


# In[2]:


import seaborn as sns
import matplotlib.pyplot as plt

sns.set_style('whitegrid')
sns.set_context('talk')

params = {'legend.fontsize': 'x-small',
          'figure.figsize': (15, 5),
          'axes.labelsize': 'small',
          'axes.titlesize':'small',
          'xtick.labelsize':'small',
          'ytick.labelsize':'small'}

plt.rcParams.update(params)


# In[3]:


data=pd.read_csv('2025-04-15 174425.csv')
# 首先获取数据集的行数
total_rows = len(data)

# 生成时间序列，确保生成足够的时间点
time_series = pd.date_range(start='2025/04/15 16:20:00', end='2025/04/15 17:43:00', freq='S')

# 确保时间序列与数据集的行数一致
time_series = time_series[:total_rows]

# 直接修改原始数据集的时间列
data['Record_TIME'] = time_series

# 将时间列设置为索引
data.index = pd.to_datetime(data['Record_TIME']) 

# 将时间列转换为日期时间类型
data['Record_TIME'] = pd.to_datetime(data['Record_TIME'])

# 将时间列设置为索引
data.set_index('Record_TIME', inplace=True)
data


# In[4]:


data = data[['CH2']]
data


# In[5]:


from sklearn.preprocessing import StandardScaler

# 使用StandardScaler标准化数据
scaler = StandardScaler()
data_scaled = scaler.fit_transform(data)

look_back = 100
X, y = [], []
for i in range(len(data_scaled) - look_back):
    X.append(data_scaled[i:(i + look_back), 0])
    y.append(data_scaled[i + look_back, 0])
X, y = np.array(X), np.array(y)

# 将数据分为训练集和测试集
train_size = int(len(X) * 0.8)
test_size = len(X) - train_size
X_train, X_test = X[0:train_size], X[train_size:len(X)]
y_train, y_test = y[0:train_size], y[train_size:len(y)]


# 调整输入数据的形状以符合CNN-LSTM模型的要求
X_train = np.reshape(X_train, (X_train.shape[0], X_train.shape[1], 1))
X_test = np.reshape(X_test, (X_test.shape[0], X_test.shape[1], 1))

# 确保数据集分布一致
def check_data_distribution(X_train, y_train, X_test, y_test):
    print(f'X_train mean: {np.mean(X_train)}, std: {np.std(X_train)}')
    print(f'y_train mean: {np.mean(y_train)}, std: {np.std(y_train)}')
    print(f'X_test mean: {np.mean(X_test)}, std: {np.std(X_test)}')
    print(f'y_test mean: {np.mean(y_test)}, std: {np.std(y_test)}')

check_data_distribution(X_train, y_train, X_test, y_test)


# In[6]:


import random
from tensorflow.keras.callbacks import EarlyStopping

# 设置随机种子以确保结果可重复
def set_seed(seed=42):
    np.random.seed(seed)
    random.seed(seed)
    tf.random.set_seed(seed)
set_seed()

# 建立改进的CNN-BiLSTM模型
model = Sequential()
model.add(Conv1D(filters=128, kernel_size=5, activation='relu', input_shape=(look_back, 1)))
model.add(MaxPooling1D(pool_size=2))
model.add(Dropout(0.3))  # 增加Dropout层
model.add(Bidirectional(LSTM(256, return_sequences=True)))
model.add(Dropout(0.3))
model.add(Bidirectional(LSTM(128)))
model.add(Dense(1))

# 编译模型
model.compile(optimizer='adam', loss='mean_squared_error')

# 创建 EarlyStopping 回调
early_stopping = EarlyStopping(monitor='val_loss', patience=3, restore_best_weights=True)

# 训练模型，并记录损失值和验证损失值的历史
history = model.fit(X_train, y_train,
                    epochs=100, batch_size=32, verbose=1,
                    validation_data=(X_test, y_test),
                    callbacks=[early_stopping])


# In[7]:


# 绘制损失和验证损失图表
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()
plt.show()


# In[8]:


# 预测
predicted = model.predict(X_test)


# In[9]:


# 将预测结果反归一化
predicted = scaler.inverse_transform(predicted)
y_test = scaler.inverse_transform(np.reshape(y_test, (-1, 1)))


# In[10]:


# 计算均方根误差
rmse = np.sqrt(mean_squared_error(y_test, predicted))
print("均方根误差：%.2f" % rmse)


# In[11]:


plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置字体为中文黑体

# 绘制实际值和预测值曲线
plt.figure(figsize=(15, 5))  # 设置图形大小

# 绘制实际值曲线
plt.plot(y_test, color='blue', linestyle='-', linewidth=2, label='实际值')

# 绘制预测值曲线
plt.plot(predicted, color='red', linestyle='--', linewidth=2, label='预测值')

# 添加轴标签和图例
plt.xlabel('样本', fontsize=16)  # x轴标签
plt.ylabel('温度', fontsize=16)      # y轴标签
plt.title('实际值和预测值曲线', fontsize=18)  # 图标题
plt.legend(fontsize=12)  # 图例字体大小

# 显示网格线
plt.grid(True)

# 显示图形
plt.tight_layout()  # 调整布局，避免标签重叠
plt.show()


# In[12]:


from sklearn import metrics

# 使用metrics模块中的函数
def evaluation_metric(y_test,y_hat):
    MSE = metrics.mean_squared_error(y_test, y_hat)
    RMSE = MSE**0.5
    MAE = metrics.mean_absolute_error(y_test,y_hat)
    R2 = metrics.r2_score(y_test,y_hat)
    print('MSE: %.5f' % MSE)
    print('RMSE: %.5f' % RMSE)
    print('MAE: %.5f' % MAE)
    print('R2: %.5f' % R2)
    
evaluation_metric(y_test, predicted)


# In[13]:


from sklearn.metrics import mean_squared_error, mean_absolute_error

# 计算模型性能指标
def mape(y_true, y_pred):
    return np.mean(np.abs((y_true - y_pred) / y_true)) * 100

def rmse(y_true, y_pred):
    return np.sqrt(mean_squared_error(y_true, y_pred))

def mae(y_true, y_pred):
    return mean_absolute_error(y_true, y_pred)

def nse(y_true, y_pred):
    numerator = np.sum((y_true - y_pred) ** 2)
    denominator = np.sum((y_true - np.mean(y_true)) ** 2)
    return 1 - numerator / denominator
   

print("MAPE: {:.5f}".format(mape(y_test, predicted)))
print("RMSE: {:.5f}".format(rmse(y_test, predicted)))
print("MAE: {:.5f}".format(mae(y_test, predicted)))
print("NSE: {:.5f}".format(nse(y_test, predicted)))


# In[58]:


from sklearn.metrics import r2_score

# 计算 R² 值
r2 = r2_score(y_test, predicted)
# 格式化 R² 值字符串
r2_str = f'$R^2$ = {r2:.4f}'

# 计算拟合直线的斜率和截距
slope, intercept = np.polyfit(y_test.flatten(), predicted.flatten(), 1)

# 创建图形
plt.figure(figsize=(15, 5))  # 指定图形大小

# 绘制散点图
scatter = plt.scatter(y_test.flatten(), predicted.flatten(), label='Data Points', color='blue', alpha=0.7, edgecolors='black')

# 绘制拟合直线
fit_line, = plt.plot(y_test.flatten(), slope * y_test.flatten() + intercept, color='red', linestyle='-', linewidth=2)

# 绘制 y=x 曲线
x_values = np.linspace(min(y_test.min(), predicted.min()), max(y_test.max(), predicted.max()), 100)
plt.plot(x_values, x_values, color='black', linestyle='--', label='Y=T')

# 设置坐标轴标签和标题
plt.xlabel('Observations', fontsize=16)  # 设置x轴标签，字体大小为12
plt.ylabel('Predictions', fontsize=16)   # 设置y轴标签，字体大小为12
plt.title('Linear Fit between Observations and Predictions', fontsize=18)  # 设置标题，字体大小为14

# 添加图例，包含数据点和拟合直线
plt.legend(handles=[scatter, fit_line], labels=['Data Points', f'Fitted Line: $y = {slope:.2f}x + {intercept:.2f}$ ({r2_str})'], loc='best', fontsize=16)

# 设置坐标轴刻度标签的字体大小
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)

# 显示图形
plt.tight_layout()  # 调整布局，避免标签重叠
# plt.savefig('scatter_and_fit_line.png', dpi=300)  # 保存为图片，设置dpi为300，以保证高清晰度
plt.show()


# In[59]:


# 将预测值保存到.csv文件
df = pd.DataFrame({'Actual': y_test.flatten(), 'Predicted': predicted.flatten()})
df.to_csv('cnn-bilstm predictions.csv', index=False)


# In[60]:


df.describe()


# In[61]:


import pandas as pd

# 读取数据集
df = pd.read_csv('cnn-bilstm predictions.csv')

# 计算差值
df['Difference'] = abs(df['Actual'] - df['Predicted'])

# 找到差值绝对值最大的样本
max_difference_row = df.loc[df['Difference'].idxmax()].round(4)

# 输出差值最大的样本
print("最大差值的样本：")
print(max_difference_row)


# In[62]:


# 计算每个样本的误差
df['Error'] = df['Actual'] - df['Predicted']

# 计算平均误差
mean_error = df['Error'].abs().mean().round(4)

# 输出平均误差
print("数据集的平均误差：", mean_error)


# In[63]:


plt.rcParams['axes.unicode_minus'] = False      # 解决负号问题
from scipy.stats import norm

# 设置字体为 Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'

# 计算预测值和实际值之间的误差
errors = df['Actual'] - df['Predicted']

# 创建图形
plt.figure(figsize=(15, 5))  # 设置图像尺寸为 15x5 英寸

# 绘制误差直方图
plt.hist(errors, bins=20, color='skyblue', edgecolor='black', density=True, label='Prediction Errors Histogram')

# 计算正态分布的参数
mu, std = norm.fit(errors)

# 生成正态分布曲线的 x 值
xmin, xmax = plt.xlim()
x = np.linspace(xmin, xmax, 100)
p = norm.pdf(x, mu, std)

# 绘制正态分布曲线
plt.plot(x, p, 'k', linewidth=2, label=f'Normal Fit ($\mu$={mu:.2f}, $\sigma$={std:.2f})')

# 添加标题和坐标轴标签
plt.title('Prediction Errors Histogram with Normal Fit', fontsize=18)
plt.xlabel('Error', fontsize=16)
plt.ylabel('Density', fontsize=16)

# 设置坐标轴刻度标签的字体大小
plt.xticks(fontsize=16)
plt.yticks(fontsize=16)

# 添加网格线
plt.grid(True)

# 添加图例
plt.legend(fontsize=16)

# 调整布局
plt.tight_layout()

# 保存图像
# plt.savefig('prediction_errors_histogram_with_normal_fit.png', dpi=300)  # 设置分辨率为 300 dpi

# 显示图形
plt.show()


# In[64]:


# 计算误差和残差
df['Error'] = df['Actual'] - df['Predicted']
df['Residuals'] = df['Actual'] - df['Predicted']

# 设置图形美化风格
sns.set(style="whitegrid")

# 散点图
plt.figure(figsize=(14, 7))


sns.scatterplot(x='Actual', y='Predicted', data=df, color='blue', s=50)
plt.plot([df['Actual'].min(), df['Actual'].max()], [df['Actual'].min(), df['Actual'].max()], 'r--')
plt.xlabel('Actual Values')
plt.ylabel('Predicted Values')
plt.title('Scatter Plot of Actual vs Predicted')
plt.legend(['Ideal', 'Predicted vs Actual'])

plt.tight_layout()
plt.show()


# In[65]:


# 误差图
plt.plot(df['Error'], color='orange', linewidth=2)
plt.xlabel('Index')
plt.ylabel('Error')
plt.title('Error Plot')
plt.axhline(0, color='r', linestyle='--', linewidth=1)
plt.legend(['Error'])


# In[66]:


# 残差图
sns.scatterplot(x='Predicted', y='Residuals', data=df, color='purple', s=50)
plt.axhline(0, color='r', linestyle='--', linewidth=1)
plt.xlabel('Predicted Values')
plt.ylabel('Residuals (Actual - Predicted)')
plt.title('Residual Plot')
plt.legend(['Zero Error', 'Residuals'])


# In[ ]:




