#!/usr/bin/env dash

echo "=== Debug rm test ==="

rm -rf .mygit
rm -f a b c d e

python3 mygit-init
echo 1 > a
echo 2 > b  
echo 3 > c
python3 mygit-add a b c
python3 mygit-commit -m "first commit"

echo 4 >> a
echo 5 >> b
echo 6 >> c
echo 7 > d
echo 8 > e
python3 mygit-add b c d
echo 9 > b

echo "Current state:"
echo "a: repo=1, index=1, working=1+4"
echo "b: repo=2, index=2+5, working=9"  
echo "c: repo=3, index=3+6, working=3+6"
echo "d: repo=none, index=7, working=7"
echo "e: repo=none, index=none, working=8"
echo ""

echo "Testing rm a:"
python3 mygit-rm a
echo ""

echo "Testing rm b:"
python3 mygit-rm b
echo ""

echo "Testing rm c:"
python3 mygit-rm c
echo ""

echo "Testing rm d:"
python3 mygit-rm d
echo ""

echo "Testing rm e:"
python3 mygit-rm e
echo ""
