import heapq

class KShortestPathsQuery(object):
    def __init__(self):
        pass

    @staticmethod
    def query(G, s, t, k):
        """
        修复版本的k最短简单路径算法
        针对NY数据集准确率问题进行优化
        """
        if s == t:
            return [[s]] if k > 0 else []

        # 构建邻接表和边权重字典
        adj_list = [[] for _ in range(G.vertex_num)]
        edge_weights = {}

        for u in range(G.vertex_num):
            for v, w in G.adj_list_out[u]:
                adj_list[u].append((v, w))
                edge_weights[(u, v)] = w

        # 找到第一条最短路径
        first_path, first_dist = KShortestPathsQuery._shortest_path(adj_list, s, t, G.vertex_num)

        if not first_path:
            return []

        if k == 1:
            return [first_path]

        # 初始化结果和候选路径
        result_paths = [first_path]
        candidates = []  # (weight, path_id, path)
        path_counter = 0
        used_paths = {tuple(first_path)}

        # Yen算法主循环
        while len(result_paths) < k:
            # 为每条已找到的路径生成偏离路径
            for path_idx in range(len(result_paths)):
                current_path = result_paths[path_idx]

                # 在每个可能的偏离点生成候选路径
                for spur_idx in range(len(current_path) - 1):
                    spur_node = current_path[spur_idx]
                    root_path = current_path[:spur_idx + 1]

                    # 收集需要排除的边
                    forbidden_edges = set()
                    for existing_path in result_paths:
                        if (len(existing_path) > spur_idx + 1 and
                            existing_path[:spur_idx + 1] == root_path):
                            forbidden_edges.add((existing_path[spur_idx], existing_path[spur_idx + 1]))

                    # 收集需要排除的节点（根路径中除偏离点外的所有节点）
                    forbidden_nodes = set(root_path[:-1])

                    # 从偏离点找到目标的最短路径
                    spur_path, spur_dist = KShortestPathsQuery._shortest_path_with_exclusions(
                        adj_list, spur_node, t, G.vertex_num, forbidden_nodes, forbidden_edges
                    )

                    if spur_path:
                        # 构建完整的候选路径
                        total_path = root_path[:-1] + spur_path

                        # 验证路径有效性
                        if (len(total_path) >= 2 and
                            total_path[0] == s and
                            total_path[-1] == t and
                            len(total_path) == len(set(total_path))):  # 简单路径检查

                            path_tuple = tuple(total_path)
                            if path_tuple not in used_paths:
                                # 计算总权重
                                total_weight = KShortestPathsQuery._calculate_path_weight(
                                    total_path, edge_weights
                                )

                                if total_weight < float('inf'):
                                    used_paths.add(path_tuple)
                                    path_counter += 1
                                    heapq.heappush(candidates, (total_weight, path_counter, total_path))

            # 选择下一条最短路径
            if not candidates:
                break

            _, _, next_path = heapq.heappop(candidates)
            result_paths.append(next_path)

        return result_paths
    
    @staticmethod
    def _dijkstra(adj_list, start, target, n):
        """优化的Dijkstra算法"""
        dist = array('d', [float('inf')] * n)
        dist[start] = 0.0
        prev = array('i', [-1] * n)
        visited = [False] * n
        pq = [(0.0, start)]

        while pq:
            current_dist, u = heapq.heappop(pq)

            if u == target:
                break

            if visited[u] or current_dist > dist[u]:
                continue

            visited[u] = True

            for v, w in adj_list[u]:
                if not visited[v]:
                    new_dist = dist[u] + w
                    if new_dist < dist[v]:
                        dist[v] = new_dist
                        prev[v] = u
                        heapq.heappush(pq, (new_dist, v))

        return dist, prev
    

    
    @staticmethod
    def _dijkstra_with_exclusions(adj_list, start, target, excluded_nodes, excluded_edges, n):
        """带排除条件的标准Dijkstra算法"""
        dist = array('d', [float('inf')] * n)
        dist[start] = 0.0
        prev = array('i', [-1] * n)
        visited = [False] * n

        # 预先标记排除的节点为已访问
        for node in excluded_nodes:
            if node != target and node != start:
                visited[node] = True

        pq = [(0.0, start)]

        while pq:
            current_dist, u = heapq.heappop(pq)

            if u == target:
                break

            if visited[u] or current_dist > dist[u]:
                continue

            visited[u] = True

            for v, w in adj_list[u]:
                if (u, v) in excluded_edges or visited[v]:
                    continue

                new_dist = dist[u] + w
                if new_dist < dist[v]:
                    dist[v] = new_dist
                    prev[v] = u
                    heapq.heappush(pq, (new_dist, v))

        return dist, prev
