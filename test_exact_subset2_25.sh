#!/usr/bin/env dash

echo "=== Testing EXACT subset2_25 scenario ==="

# Create clean test directory
mkdir -p test_exact_subset2_25_dir
cd test_exact_subset2_25_dir

# Reproduce EXACT subset2_25 scenario
python3 ../mygit-init
echo line 1 > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
echo line 2 >> a
echo hello > b
python3 ../mygit-add a b
python3 ../mygit-commit -m commit-1
python3 ../mygit-checkout b1

echo "After checkout to b1, file a content:"
cat a
echo "Expected: line 1"

echo ""
echo "Debug info:"
echo "b1 branch points to commit:"
cat .mygit/branches/b1
echo "trunk branch points to commit:"
cat .mygit/branches/trunk
echo "Current index:"
cat .mygit/index
echo "Commit 0 index:"
cat .mygit/commits/0/index
echo "Commit 1 index:"
cat .mygit/commits/1/index

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_exact_subset2_25_dir
