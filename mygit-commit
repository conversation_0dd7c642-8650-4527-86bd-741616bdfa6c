#!/usr/bin/env python3

import os
import sys
import shutil
import time

def error_exit(message):
    print(f"mygit-commit: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_commit_count():
    with open('.mygit/commit_count', 'r') as f:
        count = int(f.read().strip())
    return count

def update_commit_count(count):
    with open('.mygit/commit_count', 'w') as f:
        f.write(str(count + 1) + '\n')

def commit_with_auto_add():
    # 读取当前索引
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]
    
    # 对于索引中的每个文件，如果当前目录中存在，则更新索引
    updated = False
    for filename in list(index_entries.keys()):
        if os.path.exists(filename):
            # 重新添加文件到索引
            from mygit_add import add_file_to_index
            add_file_to_index(filename)
            updated = True

def create_commit(message, auto_add=False):
    # 如果使用-a选项，先自动添加文件
    if auto_add:
        # 读取当前索引中的文件
        index_files = []
        if os.path.exists('.mygit/index'):
            with open('.mygit/index', 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split(' ', 1)
                        if len(parts) == 2:
                            index_files.append(parts[1])
        
        # 对于索引中的每个文件，如果当前目录中存在，则重新添加
        for filename in index_files:
            if os.path.exists(filename):
                # 重新计算哈希并更新索引
                import hashlib
                with open(filename, 'rb') as f:
                    content = f.read()
                file_hash = hashlib.sha1(content).hexdigest()
                
                # 复制到objects
                object_path = f'.mygit/objects/{file_hash}'
                shutil.copy2(filename, object_path)
                
                # 更新索引
                index_entries = {}
                if os.path.exists('.mygit/index'):
                    with open('.mygit/index', 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                parts = line.split(' ', 1)
                                if len(parts) == 2:
                                    index_entries[parts[1]] = parts[0]
                
                index_entries[filename] = file_hash
                
                with open('.mygit/index', 'w') as f:
                    for fname, fhash in sorted(index_entries.items()):
                        f.write(f"{fhash} {fname}\n")
    
    # 读取当前索引
    current_index = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        current_index[parts[1]] = parts[0]

    # 获取最后一次提交的索引
    current_branch = get_current_branch()
    branch_file = f'.mygit/branches/{current_branch}'
    last_commit_index = {}

    if os.path.exists(branch_file):
        with open(branch_file, 'r') as f:
            last_commit_num = f.read().strip()
            if last_commit_num != '-1':
                last_commit_index_file = f'.mygit/commits/{last_commit_num}/index'
                if os.path.exists(last_commit_index_file):
                    with open(last_commit_index_file, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                parts = line.split(' ', 1)
                                if len(parts) == 2:
                                    last_commit_index[parts[1]] = parts[0]

    # 比较当前索引和最后一次提交的索引
    # 注意：即使索引为空，如果上次提交不为空，也算是变化
    if current_index == last_commit_index:
        print("nothing to commit")
        return
    
    # 获取提交号
    commit_num = get_commit_count()
    
    # 创建提交目录
    commit_dir = f'.mygit/commits/{commit_num}'
    os.makedirs(commit_dir)
    
    # 复制索引文件到提交目录
    shutil.copy2('.mygit/index', f'{commit_dir}/index')
    
    # 创建提交信息文件
    with open(f'{commit_dir}/message', 'w') as f:
        f.write(message + '\n')
    
    # 创建时间戳文件
    with open(f'{commit_dir}/timestamp', 'w') as f:
        f.write(str(int(time.time())) + '\n')
    
    # 更新当前分支指向新提交
    current_branch = get_current_branch()
    with open(f'.mygit/branches/{current_branch}', 'w') as f:
        f.write(str(commit_num) + '\n')
    
    # 更新提交计数器
    update_commit_count(commit_num)
    
    print(f"Committed as commit {commit_num}")

def main():
    check_mygit_exists()
    
    if len(sys.argv) < 3:
        error_exit("usage: mygit-commit [-a] -m message")
    
    auto_add = False
    message = None
    
    i = 1
    while i < len(sys.argv):
        if sys.argv[i] == '-a':
            auto_add = True
            i += 1
        elif sys.argv[i] == '-m':
            if i + 1 >= len(sys.argv):
                error_exit("option '-m' requires an argument")
            message = sys.argv[i + 1]
            i += 2
        else:
            error_exit(f"unknown option '{sys.argv[i]}'")
    
    if message is None:
        error_exit("option '-m' is required")
    create_commit(message, auto_add)
if __name__ == '__main__':
    main()
