#!/usr/bin/env python3

import os
import sys

def error_exit(message):
    """打印错误信息并退出"""
    print(f"mygit-show: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    """检查是否在mygit仓库中"""
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_file_from_index(filename):
    """从索引中获取文件内容"""
    if not os.path.exists('.mygit/index'):
        error_exit(f"'{filename}' not found in index")
    
    with open('.mygit/index', 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2 and parts[1] == filename:
                    file_hash = parts[0]
                    object_path = f'.mygit/objects/{file_hash}'
                    if os.path.exists(object_path):
                        with open(object_path, 'r', encoding='utf-8') as obj_file:
                            return obj_file.read()
                    else:
                        error_exit(f"object {file_hash} not found")
    
    error_exit(f"'{filename}' not found in index")

def get_file_from_commit(commit_num, filename):
    """从指定提交中获取文件内容"""
    commit_dir = f'.mygit/commits/{commit_num}'
    if not os.path.exists(commit_dir):
        error_exit(f"unknown commit '{commit_num}'")
    
    index_file = f'{commit_dir}/index'
    if not os.path.exists(index_file):
        error_exit(f"commit {commit_num} has no index")
    
    with open(index_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2 and parts[1] == filename:
                    file_hash = parts[0]
                    object_path = f'.mygit/objects/{file_hash}'
                    if os.path.exists(object_path):
                        with open(object_path, 'r', encoding='utf-8') as obj_file:
                            return obj_file.read()
                    else:
                        error_exit(f"object {file_hash} not found")
    
    error_exit(f"'{filename}' not found in commit {commit_num}")

def parse_argument(arg):
    """解析参数格式 [commit:]filename"""
    if ':' in arg:
        parts = arg.split(':', 1)
        if len(parts) == 2:
            commit_part, filename = parts
            if commit_part == '':
                # 格式为 :filename，从索引获取
                return None, filename
            else:
                # 格式为 commit:filename
                try:
                    commit_num = int(commit_part)
                    return commit_num, filename
                except ValueError:
                    error_exit(f"invalid commit number '{commit_part}'")
        else:
            error_exit(f"invalid argument format '{arg}'")
    else:
        error_exit(f"invalid argument format '{arg}'")

def main():
    check_mygit_exists()
    
    if len(sys.argv) != 2:
        error_exit("usage: mygit-show [commit:]filename")
    
    arg = sys.argv[1]
    commit_num, filename = parse_argument(arg)
    
    try:
        if commit_num is None:
            # 从索引获取文件
            content = get_file_from_index(filename)
        else:
            # 从指定提交获取文件
            content = get_file_from_commit(commit_num, filename)
        
        # 输出文件内容，不添加额外的换行符
        print(content, end='')
        
    except Exception as e:
        error_exit(str(e))

if __name__ == '__main__':
    main()
