#!/usr/bin/env dash

echo "=== Testing file content in hex ==="

# Create clean test directory
mkdir -p test_hex_dir
cd test_hex_dir

# Reproduce scenario
python3 ../mygit-init
echo line 1 > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
echo line 2 >> a
echo hello > b
python3 ../mygit-add a b
python3 ../mygit-commit -m commit-1

echo "Before checkout, file a content:"
cat a
echo "Before checkout, file a hex:"
xxd a

python3 ../mygit-checkout b1

echo "After checkout, file a content:"
cat a
echo "After checkout, file a hex:"
xxd a

echo "Object file content:"
object_hash=$(cat .mygit/index | cut -d' ' -f1)
echo "Object hash: $object_hash"
xxd .mygit/objects/$object_hash

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_hex_dir
