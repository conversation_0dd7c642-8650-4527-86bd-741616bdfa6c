#!/usr/bin/env dash

# Test 7: mygit-checkout functionality

echo "=== Test 7: mygit-checkout ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "content" > file.txt
python3 mygit-add file.txt
python3 mygit-commit -m "initial" > /dev/null

python3 mygit-branch feature
python3 mygit-checkout feature
if [ $? -eq 0 ]; then
    echo "✓ mygit-checkout works"
else
    echo "✗ mygit-checkout failed"
    exit 1
fi

# Verify we're on the new branch by checking if we can switch back
python3 mygit-checkout trunk
if [ $? -eq 0 ]; then
    echo "✓ mygit-checkout switch back works"
else
    echo "✗ mygit-checkout switch back failed"
    exit 1
fi

echo "=== Test 7 PASSED ==="
