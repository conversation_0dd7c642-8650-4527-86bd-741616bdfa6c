#!/usr/bin/env dash
echo "=== Test 7: mygit-checkout ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "content" > file.txt
python3 mygit-add file.txt
python3 mygit-commit -m "initial" > /dev/null

python3 mygit-branch feature
python3 mygit-checkout feature
if [ $? -eq 0 ]; then
    echo " mygit-checkout works"
else
    echo " mygit-checkout failed"
    exit 1
fi

python3 mygit-checkout trunk
if [ $? -eq 0 ]; then
    echo " mygit-checkout switch back works"
else
    echo " mygit-checkout switch back failed"
    exit 1
fi

echo "=== Test 7 PASSED ==="
