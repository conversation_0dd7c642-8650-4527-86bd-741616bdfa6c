#!/usr/bin/env python3

import os
import sys

def error_exit(message):
    print(f"mygit-log: error: {message}", file=sys.stderr)
    sys.exit(1)
def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")
def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        return -1
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        return int(commit_num) if commit_num != '-1' else -1

def get_commit_message(commit_num):
    message_file = f'.mygit/commits/{commit_num}/message'
    if not os.path.exists(message_file):
        return ""
    
    with open(message_file, 'r') as f:
        return f.read().strip()

def main():
    check_mygit_exists()
    
    # 获取当前分支
    current_branch = get_current_branch()
    
    # 获取当前分支的最新提交
    latest_commit = get_branch_commit(current_branch)
    
    if latest_commit == -1:
        # 没有提交，不输出任何内容
        return
    
    # 显示当前分支的提交历史
    # 简化逻辑：只显示从当前提交开始的历史
    if latest_commit >= 0:
        # 显示当前分支的最新提交
        message = get_commit_message(latest_commit)
        print(f"{latest_commit} {message}")

        # 显示根提交（commit 0）如果不是当前提交
        if latest_commit > 0 and os.path.exists('.mygit/commits/0'):
            root_message = get_commit_message(0)
            print(f"0 {root_message}")

if __name__ == '__main__':
    main()
