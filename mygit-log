#!/usr/bin/env python3

import os
import sys

def error_exit(message):
    print(f"mygit-log: error: {message}", file=sys.stderr)
    sys.exit(1)
def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")
def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        return -1
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        return int(commit_num) if commit_num != '-1' else -1

def get_commit_message(commit_num):
    message_file = f'.mygit/commits/{commit_num}/message'
    if not os.path.exists(message_file):
        return ""
    
    with open(message_file, 'r') as f:
        return f.read().strip()

def main():
    check_mygit_exists()
    
    # 获取当前分支
    current_branch = get_current_branch()
    
    # 获取当前分支的最新提交
    latest_commit = get_branch_commit(current_branch)
    
    if latest_commit == -1:
        # 没有提交，不输出任何内容
        return
    
    # 显示分支历史
    # 从最新提交开始，倒序显示所有提交
    for commit_num in range(latest_commit, -1, -1):
        commit_dir = f'.mygit/commits/{commit_num}'
        if os.path.exists(commit_dir):
            message = get_commit_message(commit_num)
            print(f"{commit_num} {message}")

if __name__ == '__main__':
    main()
