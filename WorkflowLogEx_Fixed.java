package com.example.workflow;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSON;
import java.util.*;

@Path("/dw/apply")
public class WorkflowLogEx {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkflowLogEx.class);
    private static final String PROC_NAME = "pcsp_GetWFLogById";
    
    public WorkflowLogEx() {
        // 构造函数
    }
    
    @GET
    @Path("/designwin/logs/{requestid}")
    @Produces(MediaType.APPLICATION_JSON)
    public String updateRequestStatus(@PathParam("requestid") String requestid) {
        logger.info("查询日志，requestid={}", requestid);
        
        // 参数验证
        if (requestid == null || requestid.trim().isEmpty()) {
            logger.warn("requestid参数为空");
            return "{\"code\":\"400\",\"message\":\"请求参数不能为空\"}";
        }
        
        RecordSet rs = null;
        try {
            rs = new RecordSet();
            List<Map<String, String>> resultList = new ArrayList<>();
            List<String> params = Collections.singletonList(requestid.trim());
            
            logger.debug("执行存储过程: {}, 参数: {}", PROC_NAME, params);
            boolean hasResult = rs.executeProc(PROC_NAME, params);
            
            if (!hasResult) {
                logger.warn("未找到 requestid={} 的日志", requestid);
                return "{\"code\":\"404\",\"message\":\"未查到数据\"}";
            }
            
            // 处理查询结果
            while (rs.next()) {
                Map<String, String> record = new LinkedHashMap<>();
                
                // 安全获取字段值
                record.put("operator", getStringValue(rs, 1));
                record.put("deptName", getStringValue(rs, 2));
                record.put("nodename", getStringValue(rs, 3));
                record.put("remark", getStringValue(rs, 4));
                record.put("dtTime", getStringValue(rs, 5));
                record.put("eName", getStringValue(rs, 6));
                record.put("act", getStringValue(rs, 7));
                
                resultList.add(record);
            }
            
            // 构建最终结果
            Map<String, Object> finalResult = new HashMap<>();
            finalResult.put("code", resultList.isEmpty() ? "001" : "000");
            finalResult.put("message", resultList.isEmpty() ? "无数据" : "操作成功");
            finalResult.put("data", resultList);
            finalResult.put("count", resultList.size());
            
            String jsonResult = JSON.toJSONString(finalResult);
            logger.info("查询成功，返回 {} 条记录", resultList.size());
            return jsonResult;
            
        } catch (Exception e) {
            logger.error("查询日志失败，requestid={}", requestid, e);
            return "{\"code\":\"500\",\"message\":\"服务器内部错误\"}";
        } finally {
            // 确保资源释放
            if (rs != null) {
                try {
                    rs.close();
                } catch (Exception e) {
                    logger.warn("关闭RecordSet失败", e);
                }
            }
        }
    }
    
    /**
     * 安全获取字符串值
     */
    private String getStringValue(RecordSet rs, int columnIndex) {
        try {
            String value = rs.getString(columnIndex);
            return value != null ? value.trim() : "";
        } catch (Exception e) {
            logger.warn("获取第{}列数据失败", columnIndex, e);
            return "";
        }
    }
}
