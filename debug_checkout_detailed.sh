#!/usr/bin/env dash

echo "=== Detailed debug checkout ==="

# Create clean test directory
mkdir -p debug_detailed_dir
cd debug_detailed_dir

# Reproduce scenario
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1

echo "Before checkout:"
echo "Files in current directory:"
ls -la
echo "7.txt content:"
cat 7.txt

# Add debug prints to checkout
echo ""
echo "Running checkout with debug..."

# Manually check what should happen
echo "Target branch (trunk) points to commit:"
cat .mygit/branches/trunk
echo "Target commit index:"
cat .mygit/commits/0/index
echo "Target object hash: 0c9d87f7ef4f9985c4a29248250f7883e486394d"
echo "Target object content:"
cat .mygit/objects/0c9d87f7ef4f9985c4a29248250f7883e486394d

python3 ../mygit-checkout trunk

echo ""
echo "After checkout:"
echo "7.txt content:"
cat 7.txt

echo ""
echo "=== Debug completed ==="

# Clean up
cd ..
rm -rf debug_detailed_dir
