# COMP9312 25T2 Project Solutions

## 📋 项目概述

本项目包含COMP9312课程的两个核心算法实现：
1. **Q1**: Top-k最短简单路径算法
2. **Q2**: k-core基础结构多样性算法

## 🚀 Q1: Top-k最短简单路径

### 算法描述
基于修改的Yen算法实现，使用优先队列优化候选路径生成。

### 核心特性
- ✅ 保证路径简单性（无环、无重复顶点）
- ✅ 按权重递增排序
- ✅ 相同权重时按字典序排序
- ✅ 处理边界情况（无路径、k值过大等）

### 时间复杂度
**O(k × n × (m + n log n))**
- k: 所需路径数量
- n: 顶点数量  
- m: 边数量

### 算法步骤
1. 使用Dijkstra算法找到最短路径
2. 通过偏离现有路径生成候选路径
3. 使用优先队列维护候选路径
4. 重复直到找到k条路径

### 使用示例
```python
from Q1_solution import KShortestPathsQuery

# 图表示：{节点: [(邻居, 权重), ...]}
G = {
    's': [('a', 1), ('g', 10), ('h', 15)],
    'a': [('b', 2)],
    'b': [('c', 3)],
    'c': [('d', 4)],
    'd': [('e', 5)],
    'e': [('t', 6)],
    'g': [('t', 5)],
    'h': [('t', 1)]
}

query = KShortestPathsQuery()
result = query.query(G, 's', 't', k=3)
print(result)
# 输出: [['s', 'a', 'b', 'c', 'd', 'e', 't'], ['s', 'g', 't'], ['s', 'h', 't']]
```

## 🔄 Q2: k-core基础结构多样性

### 算法描述
实现高效的k-core分解算法，计算顶点邻居诱导子图中的k-core数量。

### 核心特性
- ✅ 高效k-core分解（O(m+n)时间复杂度）
- ✅ 结构多样性计算
- ✅ 支持在线查询
- ✅ 连通分量检测

### 时间复杂度
- **k-core分解**: O(m + n)
- **单顶点多样性**: O(d²)，d为最大度数
- **全图多样性**: O(n × d²)

### 算法步骤
1. 使用度数桶排序进行k-core分解
2. 对每个顶点构建邻居诱导子图
3. 在邻居子图中找到所有k-core
4. 计算连通的k-core组件数量

### 使用示例
```python
from Q2_solution import KCoreStructuralDiversity

# 边列表表示
edges = [
    (1, 2), (1, 3), (1, 4),
    (2, 3), (2, 5),
    (3, 4), (3, 5), (3, 6),
    (4, 6), (5, 6), (5, 7),
    (6, 7), (6, 8), (7, 8)
]

analyzer = KCoreStructuralDiversity(edges)

# 计算所有顶点的2-core结构多样性
diversities = analyzer.compute_all_structural_diversities(k=2)
print(diversities)

# 查询单个顶点
diversity = analyzer.compute_k_core_structural_diversity(vertex=3, k=2)
print(f"Vertex 3 的2-core结构多样性: {diversity}")
```

## 📊 性能分析

### Q1性能特点
- **优势**: 
  - 保证找到最优解
  - 内存使用相对较少
  - 支持任意权重图
- **限制**: 
  - 对于大k值性能下降
  - 密集图中候选路径数量可能很大

### Q2性能特点
- **优势**: 
  - k-core分解非常高效
  - 适合大规模图
  - 支持在线查询
- **限制**: 
  - 高度数顶点的多样性计算较慢
  - 内存使用与图大小成正比

## 🧪 测试用例

### Q1测试
```python
# 运行Q1测试
python Q1_solution.py
```

### Q2测试  
```python
# 运行Q2测试
python Q2_solution.py
```

## 📁 文件结构
```
├── Q1_solution.py          # Q1完整实现
├── Q2_solution.py          # Q2完整实现  
├── README.md               # 项目文档
└── test_cases/             # 测试用例（可选）
```

## 🔧 依赖要求
- Python 3.7+
- 标准库模块：
  - `heapq` (优先队列)
  - `collections` (数据结构)
  - `sys` (系统功能)

## 📝 算法优化建议

### Q1优化方向
1. **路径缓存**: 缓存已计算的子路径
2. **剪枝策略**: 提前终止不可能的候选路径
3. **并行化**: 并行生成候选路径

### Q2优化方向
1. **增量更新**: 支持动态图更新
2. **近似算法**: 对于超大图使用采样
3. **索引结构**: 预计算常用查询结果

## 🎯 应用场景

### Q1应用
- 交通路径规划
- 网络路由优化
- 社交网络分析
- 供应链优化

### Q2应用
- 社交网络分析
- 生物网络研究
- 基础设施网络
- 推荐系统

## 📚 参考文献
1. Yen, J. Y. (1971). Finding the k shortest loopless paths in a network.
2. Batagelj, V., & Zaversnik, M. (2003). An O(m) algorithm for cores decomposition of networks.
3. Seidman, S. B. (1983). Network structure and minimum degree.

---
**作者**: COMP9312学生  
**课程**: Data Analytics for Graphs  
**学期**: 25T2
