from collections import defaultdict, deque
class KCoreStructuralDiversityQuery:
    def __init__(self):
        self.graph = {}
        self.vertices = set()
        self.core_numbers = {}
        self.is_initialized = False
    def initialize_graph(self, edges):
        # 构建邻接表
        self.graph = defaultdict(set)
        self.vertices = set()
        for u, v in edges:
            self.graph[u].add(v)
            self.graph[v].add(u)
            self.vertices.add(u)
            self.vertices.add(v)
        # 将集合转换为列表以保持一致性
        for v in self.graph:
            self.graph[v] = list(self.graph[v])
        # 计算k-core分解
        self._compute_core_decomposition()
        self.is_initialized = True
    def _compute_core_decomposition(self):
        if not self.vertices:
            return
        # 初始化度数
        degrees = {}
        for v in self.vertices:
            degrees[v] = len(self.graph.get(v, []))
        # 创建度数桶
        max_degree = max(degrees.values()) if degrees else 0
        buckets = [[] for _ in range(max_degree + 1)]
        # 按度数将顶点放入桶中
        for v in self.vertices:
            buckets[degrees[v]].append(v)
        # 按照度数递增的顺序处理顶点
        processed = set()
        self.core_numbers = {}
        for k in range(max_degree + 1):
            while buckets[k]:
                v = buckets[k].pop()
                if v in processed:
                    continue
                processed.add(v)
                self.core_numbers[v] = k
                # 更新邻居的度数
                for u in self.graph.get(v, []):
                    if u not in processed and degrees[u] > k:
                        # 将u移到更低的桶
                        old_degree = degrees[u]
                        degrees[u] = max(k, degrees[u] - 1)
                        if degrees[u] < old_degree:
                            buckets[degrees[u]].append(u)
    def query_structural_diversity(self, vertex, k):
        if not self.is_initialized:
            raise ValueError("图未初始化。请先调用initialize_graph()。")
        if vertex not in self.graph:
            return 0
        # 获取顶点的邻居
        neighbors = set(self.graph[vertex])
        if not neighbors:
            return 0
        # 在邻居诱导子图中查找k-core
        k_cores = self._find_k_cores_in_subgraph(neighbors, k)
        return len(k_cores)
    def _find_k_cores_in_subgraph(self, vertices, k):
        if not vertices:
            return []
        # 构建诱导子图
        induced_graph = {}
        for v in vertices:
            induced_graph[v] = []
            for u in self.graph.get(v, []):
                if u in vertices:
                    induced_graph[v].append(u)
        # 使用迭代删除计算诱导子图的k-core
        degrees = {v: len(induced_graph[v]) for v in vertices}
        remaining = set(vertices)
        # 迭代删除度数<k的顶点
        changed = True
        while changed:
            changed = False
            to_remove = []
            for v in remaining:
                if degrees[v] < k:
                    to_remove.append(v)
            for v in to_remove:
                remaining.remove(v)
                changed = True
                # 更新邻居的度数
                for u in induced_graph[v]:
                    if u in remaining:
                        degrees[u] -= 1
        if not remaining:
            return []
        # 使用BFS在剩余顶点中查找连通分量
        components = []
        visited = set()
        for v in remaining:
            if v not in visited:
                component = set()
                queue = deque([v])
                while queue:
                    current = queue.popleft()
                    if current in visited:
                        continue
                    visited.add(current)
                    component.add(current)
                    # 添加剩余集合中未访问的邻居
                    for neighbor in induced_graph[current]:
                        if neighbor in remaining and neighbor not in visited:
                            queue.append(neighbor)
                if component:
                    components.append(component)
        return components
    def get_core_number(self, vertex):
        """获取顶点的核心数"""
        if not self.is_initialized:
            raise ValueError("图未初始化。")
        return self.core_numbers.get(vertex, 0)
    def compute_all_structural_diversities(self, k):
        if not self.is_initialized:
            raise ValueError("图未初始化。")
        diversities = {}
        for vertex in self.vertices:
            diversities[vertex] = self.query_structural_diversity(vertex, k)
        return diversities
# 示例使用和测试
def test_k_core_diversity():
    print("测试基于k-core的结构多样性:")
    print("=" * 50)
    # 示例图
    edges = [
        (1, 2), (1, 3), (1, 4),
        (2, 3), (2, 5),
        (3, 4), (3, 5), (3, 6),
        (4, 6),
        (5, 6), (5, 7),
        (6, 7), (6, 8),
        (7, 8)
    ]
    # 初始化查询处理器
    query = KCoreStructuralDiversityQuery()
    query.initialize_graph(edges)
    print("图边:", edges)
    print()
    # 显示核心数
    print("核心数:")
    for vertex in sorted(query.vertices):
        core_num = query.get_core_number(vertex)
        print(f"  顶点 {vertex}: 核心数 = {core_num}")
    print()
    # 测试不同k值的结构多样性
    for k in [1, 2, 3]:
        print(f"k-core结构多样性 (k={k}):")
        diversities = query.compute_all_structural_diversities(k)
        for vertex in sorted(diversities.keys()):
            diversity = diversities[vertex]
            print(f"  顶点 {vertex}: τ_{k}({vertex}) = {diversity}")
        print()
    # 测试单个查询
    print("单个查询:")
    test_vertex = 3
    for k in [1, 2]:
        diversity = query.query_structural_diversity(test_vertex, k)
        print(f"  τ_{k}({test_vertex}) = {diversity}")
if __name__ == "__main__":
    test_k_core_diversity()
