#!/usr/bin/env python3

import os
import sys

def error_exit(message):
    """打印错误信息并退出"""
    print(f"mygit-branch: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    """检查是否在mygit仓库中"""
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    """获取当前分支名"""
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_current_commit():
    """获取当前分支的提交号"""
    current_branch = get_current_branch()
    branch_file = f'.mygit/branches/{current_branch}'
    if not os.path.exists(branch_file):
        return -1
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        return int(commit_num) if commit_num != '-1' else -1

def list_branches():
    """列出所有分支"""
    current_branch = get_current_branch()
    
    if not os.path.exists('.mygit/branches'):
        return
    
    branches = []
    for filename in os.listdir('.mygit/branches'):
        if os.path.isfile(f'.mygit/branches/{filename}'):
            branches.append(filename)
    
    for branch in sorted(branches):
        if branch == current_branch:
            print(f"* {branch}")
        else:
            print(f"  {branch}")

def create_branch(branch_name):
    """创建新分支"""
    branch_file = f'.mygit/branches/{branch_name}'
    
    # 检查分支是否已存在
    if os.path.exists(branch_file):
        error_exit(f"branch '{branch_name}' already exists")
    
    # 获取当前提交号
    current_commit = get_current_commit()
    
    # 创建新分支，指向当前提交
    with open(branch_file, 'w') as f:
        f.write(str(current_commit) + '\n')

def delete_branch(branch_name):
    """删除分支"""
    current_branch = get_current_branch()
    
    # 不能删除当前分支
    if branch_name == current_branch:
        error_exit(f"can not delete branch '{branch_name}': currently on this branch")
    
    branch_file = f'.mygit/branches/{branch_name}'
    
    # 检查分支是否存在
    if not os.path.exists(branch_file):
        error_exit(f"branch '{branch_name}' doesn't exist")
    
    # 删除分支文件
    os.remove(branch_file)

def main():
    check_mygit_exists()
    
    if len(sys.argv) == 1:
        # 没有参数，列出所有分支
        list_branches()
        return
    
    if len(sys.argv) == 2:
        # 一个参数，创建分支
        branch_name = sys.argv[1]
        create_branch(branch_name)
        return
    
    if len(sys.argv) == 3 and sys.argv[1] == '-d':
        # -d 选项，删除分支
        branch_name = sys.argv[2]
        delete_branch(branch_name)
        return
    
    error_exit("usage: mygit-branch [-d] [branch-name]")

if __name__ == '__main__':
    main()
