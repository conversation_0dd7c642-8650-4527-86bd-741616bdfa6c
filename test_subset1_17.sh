#!/usr/bin/env dash

echo "=== Testing subset1_17 complete scenario ==="

rm -rf .mygit
rm -f a b

python3 mygit-init
touch a b
python3 mygit-add a b
python3 mygit-commit -m "first commit"
rm a
python3 mygit-commit -m "second commit"
python3 mygit-add a
python3 mygit-commit -m "second commit"
python3 mygit-rm --cached b
python3 mygit-commit -m "second commit"
python3 mygit-rm b
python3 mygit-add b
python3 mygit-rm b
python3 mygit-commit -m "third commit"
python3 mygit-rm b
python3 mygit-commit -m "fourth commit"

echo "=== Test completed ==="
