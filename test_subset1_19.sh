#!/usr/bin/env dash

echo "=== Testing subset1_19 --cached scenario ==="

rm -rf .mygit
rm -f a b c d e

python3 mygit-init
echo 1 > a
echo 2 > b
echo 3 > c
python3 mygit-add a b c
python3 mygit-commit -m "first commit"
echo 4 >> a
echo 5 >> b
echo 6 >> c
echo 7 > d
echo 8 > e
python3 mygit-add b c d e
echo 9 > b
echo 0 > d

echo "Testing mygit-rm --cached a c:"
python3 mygit-rm --cached a c

echo "Testing mygit-rm --force --cached b:"
python3 mygit-rm --force --cached b

echo "Testing mygit-rm --force --cached e:"
python3 mygit-rm --force --cached e

echo "Testing mygit-rm --force d:"
python3 mygit-rm --force d

echo "Testing mygit-status:"
python3 mygit-status | grep -E "^[abcde] -"

echo "=== Test completed ==="
