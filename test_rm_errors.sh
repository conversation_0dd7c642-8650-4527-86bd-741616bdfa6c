#!/usr/bin/env dash

echo "=== Testing rm error messages ==="

# Clean up
rm -rf .mygit
rm -f a b c d e

# Initialize and setup
python3 mygit-init
echo 1 > a
echo 2 > b  
echo 3 > c
python3 mygit-add a b c
python3 mygit-commit -m "first commit"

# Create different scenarios
echo 4 >> a
echo 5 >> b
echo 6 >> c
echo 7 > d
echo 8 > e
python3 mygit-add b c d
echo 9 > b

# Test different rm scenarios
echo "$ mygit-rm a"
python3 mygit-rm a

echo "$ mygit-rm b"  
python3 mygit-rm b

echo "$ mygit-rm c"
python3 mygit-rm c

echo "$ mygit-rm d"
python3 mygit-rm d

echo "$ mygit-rm e"
python3 mygit-rm e

echo "=== Test completed ==="
