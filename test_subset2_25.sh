#!/usr/bin/env dash

echo "=== Testing subset2_25 scenario ==="

# Create clean test directory
mkdir -p test_subset2_25_dir
cd test_subset2_25_dir

# Reproduce exact subset2_25 scenario
python3 ../mygit-init
echo "line 1" > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0

echo "After first commit, file a content:"
cat a

python3 ../mygit-branch b1
echo "line 2" >> a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-1

echo "After second commit on trunk, file a content:"
cat a

python3 ../mygit-checkout b1

echo "After checkout to b1, file a content:"
cat a
echo "Expected: line 1"

echo "Checking b1 branch commit:"
cat .mygit/branches/b1

echo "Checking commit 0 index:"
cat .mygit/commits/0/index

echo "Checking current index:"
cat .mygit/index

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_subset2_25_dir
