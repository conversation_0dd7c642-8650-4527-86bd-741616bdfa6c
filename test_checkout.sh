#!/usr/bin/env dash

echo "=== Testing checkout fixes ==="

# Create a clean test directory
mkdir -p test_checkout_dir
cd test_checkout_dir

# Test subset2_24 scenario
python3 ../mygit-init
touch a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
touch b
python3 ../mygit-add b
python3 ../mygit-commit -m commit-1
python3 ../mygit-checkout trunk

echo "Files after checkout to trunk:"
ls -la

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_checkout_dir
