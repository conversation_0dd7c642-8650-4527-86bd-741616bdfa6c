#!/usr/bin/env dash

echo "=== Testing checkout fixes ==="

# Create clean test directory
mkdir -p test_checkout_fix_dir
cd test_checkout_fix_dir

echo "1. Testing subset2_25 scenario:"
python3 ../mygit-init
echo line 1 > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
echo line 2 >> a
echo hello > b
python3 ../mygit-add a b
python3 ../mygit-commit -m commit-1
python3 ../mygit-checkout b1
echo "File a content after checkout to b1:"
cat a
echo "Expected: line 1"

echo ""
echo "2. Testing subset2_26 scenario:"
rm -rf .mygit *
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch b1
echo world >> a
python3 ../mygit-checkout b1
echo "File a content after checkout to b1 (should preserve modification):"
cat a
echo "Expected: hello\\nworld"
python3 ../mygit-status
echo "Expected: a - file changed, changes not staged for commit"

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_checkout_fix_dir
