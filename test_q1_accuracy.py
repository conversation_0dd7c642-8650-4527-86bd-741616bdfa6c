"""
测试Q1算法在两个数据集上的准确率和性能
"""
import time
import numpy as np
from Q1_solution import KShortestPathsQuery

class UndirectedUnweightedGraph:
    def __init__(self, vertex_num):
        self.vertex_num = vertex_num
        self.adj_list_out = [[] for _ in range(vertex_num)]
    
    def add_edge(self, u, v, w=1):
        self.adj_list_out[u].append((v, w))

def load_graph_from_edges(edge_list):
    """从边列表加载图"""
    if len(edge_list) == 0:
        return UndirectedUnweightedGraph(0)
    
    max_vertex = max(max(edge) for edge in edge_list)
    graph = UndirectedUnweightedGraph(max_vertex + 1)
    
    for u, v in edge_list:
        graph.add_edge(u, v, 1)  # 假设权重为1
    
    return graph

def test_small_example():
    """测试小例子验证算法正确性"""
    print("🧪 测试小例子...")
    
    # 创建简单测试图
    graph = UndirectedUnweightedGraph(5)
    # 添加边: 0->1->2->4, 0->3->4
    graph.add_edge(0, 1, 1)
    graph.add_edge(1, 2, 1) 
    graph.add_edge(2, 4, 1)
    graph.add_edge(0, 3, 2)
    graph.add_edge(3, 4, 1)
    
    query = KShortestPathsQuery()
    
    # 测试k=1
    result1 = query.query(graph, 0, 4, 1)
    print(f"k=1: {result1}")
    
    # 测试k=2
    result2 = query.query(graph, 0, 4, 2)
    print(f"k=2: {result2}")
    
    # 验证结果
    if len(result1) == 1 and len(result1[0]) >= 2:
        print("✅ k=1测试通过")
    else:
        print("❌ k=1测试失败")
    
    if len(result2) >= 1:
        print("✅ k=2测试通过")
    else:
        print("❌ k=2测试失败")

def test_performance_and_accuracy():
    """测试性能和准确率"""
    print("\n🚀 性能和准确率测试...")
    
    # 模拟测试数据
    print("创建模拟测试数据...")
    
    # 小规模测试
    small_edges = [(i, i+1) for i in range(100)]  # 链状图
    small_graph = load_graph_from_edges(small_edges)
    
    query = KShortestPathsQuery()
    
    # 测试查询
    test_queries = [
        (0, 50, 3),
        (10, 80, 5),
        (20, 90, 2)
    ]
    
    total_time = 0
    success_count = 0
    
    for s, t, k in test_queries:
        start_time = time.time()
        try:
            result = query.query(small_graph, s, t, k)
            end_time = time.time()
            
            query_time = end_time - start_time
            total_time += query_time
            
            # 验证结果
            if len(result) > 0 and all(len(path) >= 2 for path in result):
                success_count += 1
                print(f"✅ 查询({s}, {t}, {k}): {len(result)}条路径, 时间: {query_time:.4f}s")
            else:
                print(f"❌ 查询({s}, {t}, {k}): 结果无效")
                
        except Exception as e:
            print(f"❌ 查询({s}, {t}, {k}): 异常 - {e}")
    
    avg_time = total_time / len(test_queries)
    accuracy = success_count / len(test_queries)
    
    print(f"\n📊 测试结果:")
    print(f"平均查询时间: {avg_time:.4f}s")
    print(f"准确率: {accuracy:.2%}")
    print(f"成功查询: {success_count}/{len(test_queries)}")

def validate_path_properties(paths, s, t):
    """验证路径属性"""
    if not paths:
        return True, "无路径"
    
    issues = []
    
    for i, path in enumerate(paths):
        # 检查起点和终点
        if len(path) < 2:
            issues.append(f"路径{i}: 长度不足")
            continue
            
        if path[0] != s:
            issues.append(f"路径{i}: 起点错误")
        if path[-1] != t:
            issues.append(f"路径{i}: 终点错误")
        
        # 检查简单路径
        if len(path) != len(set(path)):
            issues.append(f"路径{i}: 不是简单路径")
        
        # 检查路径唯一性
        for j in range(i+1, len(paths)):
            if path == paths[j]:
                issues.append(f"路径{i}和{j}: 重复路径")
    
    return len(issues) == 0, issues

def main():
    """主测试函数"""
    print("🔧 Q1算法准确率和性能测试")
    print("=" * 50)
    
    test_small_example()
    test_performance_and_accuracy()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    
    # 提供优化建议
    print("\n💡 优化建议:")
    print("1. 确保路径验证逻辑正确")
    print("2. 检查权重计算精度")
    print("3. 优化候选路径生成策略")
    print("4. 加强去重机制")

if __name__ == "__main__":
    main()
