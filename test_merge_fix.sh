#!/usr/bin/env dash

echo "=== Testing merge fix ==="

# Create clean test directory
mkdir -p test_merge_fix_dir
cd test_merge_fix_dir

# Reproduce subset2_30 scenario
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1
python3 ../mygit-checkout trunk
python3 ../mygit-merge b1 -m merge-message

echo "After merge, file 7.txt content:"
cat 7.txt
echo ""
echo "Expected:"
seq 1 7 | sed 's/2/42/'

python3 ../mygit-log
python3 ../mygit-status

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_merge_fix_dir
