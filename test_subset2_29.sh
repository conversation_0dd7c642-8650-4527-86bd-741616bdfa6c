#!/usr/bin/env dash

echo "=== Testing subset2_29 scenario ==="

# Create clean test directory
mkdir -p test_subset2_29_dir
cd test_subset2_29_dir

# Reproduce exact subset2_29 scenario
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch branchA
echo world > b
python3 ../mygit-add b
python3 ../mygit-commit -m commit-B
python3 ../mygit-checkout branchA
echo "new contents" > b
python3 ../mygit-checkout trunk

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_subset2_29_dir
