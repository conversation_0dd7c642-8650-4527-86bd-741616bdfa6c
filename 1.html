




<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/>

    <title>
        COMP(2041|9044) 25T2 —
        
Assignment 2: MyGit

    </title><link rel="canonical" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/"/>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.0/css/bootstrap.min.css"
          integrity="sha256-aAr2Zpq8MZ+YA/D6JtRD3xtrwpEz2IqOS+pWD/7XKIw=" crossorigin="anonymous"/>
    <link rel="stylesheet" href="/~cs2041/25T2/flask.cgi/static/course.css?209281748402686.4147782="/>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"
            integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.1/umd/popper.min.js"
            integrity="sha256-/ijcOLwFf26xEYAjW75FizKVo5tnTYiQddPZoLUHHZ8=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.0/js/bootstrap.min.js"
            integrity="sha256-OFRAJNoaD8L3Br5lglV7VyLRf0itmoBzWUoM+Sji4/8=" crossorigin="anonymous"></script>

    <script src="/~cs2041/25T2/flask.cgi/static/course.js?101671748402686.4187782="></script>

    <link rel="icon" type="image/png" href="/~cs2041/25T2/flask.cgi/static/favicon.ico?21571748402686.4187782=">

    <!-- <script async src="https://static.codepen.io/assets/embed/ei.js"></script> -->

    <!-- MathJax. -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.6/latest.js?config=TeX-MML-AM_CHTML"
            async="yes"></script>

    
<link
  rel="stylesheet"
  href="https://cdn.rawgit.com/afeld/bootstrap-toc/v1.0.1/dist/bootstrap-toc.min.css"
/>
<!-- add after bootstrap.min.js -->
<script src="https://cdn.rawgit.com/afeld/bootstrap-toc/v1.0.1/dist/bootstrap-toc.min.js"></script>


    
</head>
<body class="d-flex flex-column" style="min-height: 100vh;">
<nav class="navbar fixed-top navbar-expand-lg navbar-light no-print" id="header-navbar">
    <div class="container">
        <a class="navbar-brand" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/">
            COMP(2041|9044) - 25T2
        </a>
        <button class="navbar-toggler navbar-toggler-right"
                type="button" data-toggle="collapse" data-target="#navmenu"
                aria-controls="navmenu" aria-label="Toggle navigation" aria-expanded="false">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navmenu">
            <ul class="navbar-nav mr-auto">
                
                    
                        <li class="nav-item active">
                            <a class="nav-link" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/outline/">
                                Course Outline
                            </a>
                        </li>
                    
                        <li class="nav-item active">
                            <a class="nav-link" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/timetable/">
                                Course Timetable
                            </a>
                        </li>
                    
                        <li class="nav-item active">
                            <a class="nav-link" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/forum/">
                                Course Forum
                            </a>
                        </li>
                    
                        <li class="nav-item active">
                            <a class="nav-link" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/assignments/ass1/index.html">
                                Assignment 1
                            </a>
                        </li>
                    
                        <li class="nav-item active">
                            <a class="nav-link" href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/assignments/ass2/index.html">
                                Assignment 2
                            </a>
                        </li>
                    
                    
                    
                
            </ul>
        </div>
    </div>
</nav>
<main class="container" aria-label="Content" style="flex: 1; padding-top: 4rem;">
    

        <header>
            
                <h1 class="text-center">
Assignment 2: MyGit
</h1>
            
        </header>

        
<style>
    h1::before,h2::before {
      content: '';
      display: block;
      height:      50px;
      margin-top: -50px;
      visibility: hidden;
    }

    #toc {
      position: -webkit-sticky; /* Safari */
      position: sticky;
      top: 70px;
    }

    main[aria-label="Content"] {
        max-width: 70rem;
    }
</style>

<div class="row" id="assignment-body">
<div class="col-lg-10 col-12">


















<p class="text-right text-muted"><small>
version: 1.0
last updated: 2025-07-23 10:00
</small></p>



















<h2>Aims</h2>

<p>
This assignment aims to give you
<ul>
<li> practice in Python programming generally
<li> practice in system programming in Python, particularly manipulating files and directories<li> a clear concrete understanding of Git's core semantics
</ul>

<p>
<strong>Note</strong>:
the material in the lecture notes
will not be sufficient by itself
to allow you to complete this assignment.
You may need to search online documentation for Python, Git, etc.
Being able to search documentation efficiently
for the information you need
is a <em>very</em> useful skill
for any kind of computing work.

<h2>Introduction</h2>
<p>
In this assignment, you will
implement <strong>MyGit</strong>,
a  simple but functional subset of the version control system <a href="https://en.wikipedia.org/wiki/Git">Git</a>.
<p>
Git is a <em>very</em> complex program that has many individual commands.
You will implement only a few of the most important commands.
You will also be given a number of simplifying assumptions,
which make your task easier.


<p>
Your task in this assignment is to write 10 Python scripts named:
<code>mygit-init</code>
<code>mygit-add</code>
<code>mygit-commit</code>
<code>mygit-log</code>
<code>mygit-show</code>
<code>mygit-rm</code>
<code>mygit-status</code>
<code>mygit-branch</code>
<code>mygit-checkout</code>
<code>mygit-merge</code>


<h2>Reference implementation</h2>
<p>
Many aspects of this assignment
are not fully specified in this document; instead,
you must match the behaviour of a reference implementation.

<p>
For example, your script <code>mygit-add</code>
should match the behaviour of <code>2041 mygit-add</code> exactly,
including producing the same error messages.

<p>
Provision of a reference implementation is
a common method to provide or define
an operational specification,
and it's something you will likely need to do after you leave UNSW.

<p>
Discovering and matching
the reference implementation's behaviour
is deliberately part of the assignment.

<p>
While the code in the reference implementation is fairly straightforward,
reverse-engineering its behaviour
is obviously not so simple,
and is a nice example of
how coming to grips with
the precise semantics of
an apparently obvious task
can still be challenging.

<p>
If you discover what you believe to be
a bug in the reference implementation,
report it in the class forum.
We may fix the bug,
or indicate that you do not need to match
the reference implementation's behaviour in this case.


<h2>MyGit Commands</h2>
<p>
All commands must be implemented in Python only.
<p>
See the <strong>Permitted Languages</strong> section for more information.

<h3>Subset 0</h3>

<h4>mygit-init</h4>
<p>
The <code>mygit-init</code> command creates an empty MyGit repository.

<p>
<code>mygit-init</code> should create a directory named <code>.mygit</code>,
which it will use to store the repository.
<br />
It should produce an error message if this directory already exists, or cannot be created.

<p>
You should match this, and other error messages exactly.
For example:


    
        
    
<pre is="tty" style="max-height: 32em; overflow: auto;" ><kbd is="sh">ls -d .mygit</kbd>
ls: cannot access '.mygit': No such file or directory
<kbd is="sh">./mygit-init</kbd>
Initialized empty mygit repository in .mygit
<kbd is="sh">ls -d .mygit</kbd>
.mygit
<kbd is="sh">./mygit-init</kbd>
mygit-init: error: .mygit already exists
</pre>


<p>
<code>mygit-init</code> may create initial files or directories inside <code>.mygit</code>.

<p>
You do not have to use a particular representation to store the repository.

<p>
You do not have to (and should not) create the same files and directories inside <code>.mygit</code> as the reference implementation.
<br />
You can create whatever files or directories inside <code>.mygit</code> you wish.
<p>
Do not store information outside <code>.mygit</code>


<h4>mygit-add <var>filenames...</var></h4>
<p>
The <code>mygit-add</code> command
adds the contents of one or more files
to the <q><strong>index</strong></q>.

<p>
Files are added to the repository in a two-step process.
The first step is adding them to the index.

<p>
You will need to store files in the index somehow
in the <code>.mygit</code> sub-directory.
<br />
For example, you might choose to store them in
a sub-directory of <code>.mygit</code>.

<p>
Only ordinary files in the current directory can be added.
<br />
You can assume filenames start with an alphanumeric character (<code>[a-zA-Z0-9]</code>)
and will only contain alpha-numeric characters,
plus <code>.</code>, <code>-</code> and <code>_</code> characters.

<p>
The <code>mygit-add</code> command,
and other MyGit commands,
will not be given pathnames with slashes.


<h4>mygit-commit -m <var>message</var></h4>
<p>
The <code>mygit-commit</code> command
saves a copy of all files in the index to the repository.

<p>
A message describing the commit must be included as part of the commit command.

<p>
MyGit commits are numbered sequentially:
they are not hashes, like Git.
You must match the numbering scheme.

<p>
You can assume the commit message is ASCII,
does not contain new-line characters,
and does not start with a <code>-</code> character.


<h4>mygit-log</h4>
<p>
The <code>mygit-log</code> command prints
a line for every commit made to the repository.
<br />
Each line should contain the commit number
and the commit message.


<h4>mygit-show <var>[commit]</var>:<var>filename</var></h4>
<p>
The <code>mygit-show</code> should
print the contents of the specified <var>filename</var>
as of the specified <var>commit</var>.
<br />
If <var>commit</var> is omitted,
the contents of the file in the index
should be printed.
<p>
You can assume the commit, if specified, will be a non-negative integer.
<p>

<h3>Subset 0 examples</h3>


    
        
    
<pre is="tty" style="max-height: 32em; overflow: auto;" ><kbd is="sh">./mygit-init</kbd>
Initialized empty mygit repository in .mygit
<kbd is="sh">echo line 1 &gt; a</kbd>
<kbd is="sh">echo hello world &gt;b</kbd>
<kbd is="sh">./mygit-add a b</kbd>
<kbd is="sh">./mygit-commit -m 'first commit'</kbd>
Committed as commit 0
<kbd is="sh">echo  line 2 &gt;&gt;a</kbd>
<kbd is="sh">./mygit-add a</kbd>
<kbd is="sh">./mygit-commit -m 'second commit'</kbd>
Committed as commit 1
<kbd is="sh">./mygit-log</kbd>
1 second commit
0 first commit
<kbd is="sh">echo line 3 &gt;&gt;a</kbd>
<kbd is="sh">./mygit-add a</kbd>
<kbd is="sh">echo line 4 &gt;&gt;a</kbd>
<kbd is="sh">./mygit-show 0:a</kbd>
line 1
<kbd is="sh">./mygit-show 1:a</kbd>
line 1
line 2
<kbd is="sh">./mygit-show :a</kbd>
line 1
line 2
line 3
<kbd is="sh">cat a</kbd>
line 1
line 2
line 3
line 4
<kbd is="sh">./mygit-show 0:b</kbd>
hello world
<kbd is="sh">./mygit-show 1:b</kbd>
hello world
</pre>



<h3>Subset 1</h3>
<p>
Subset 1 is more difficult.
You will need to spend some time
understanding the semantics (meaning) of these operations,
by running the reference implementation,
or researching the equivalent Git operations.

<p>
Note the assessment scheme recognises this difficulty.
You can obtain a <b>CR</b> for the assignment without completing subset 1.


<h4>mygit-commit <var>[-a]</var> -m <var>message</var></h4>
<p>
<code>mygit-commit</code> can now have a <code>-a</code> option,
<br />
which causes all files already in the index
to have their contents from the current directory
added to the index before the commit.


<h4>mygit-rm <var>[--force]</var> <var>[--cached]</var> <var>filenames...</var></h4>
<p>
<code>mygit-rm</code> removes a file from the index,
or, from the current directory and the index.

<p>
If the <code>--cached</code> option is specified,
the file is removed only from the index,
and not from the current directory.

<p>
<code>mygit-rm</code>, like <code>git rm</code>,
should stop the user accidentally losing work,
and should give an error message instead
if the removal would cause the user to lose work.
You will need to experiment with the reference implementation
to discover these error messages.
Researching <code>git rm</code>'s behaviour may also help.

<p>
The <code>--force</code> option overrides this,
and will carry out the removal even if the user will lose work.


<h4>mygit-status</h4>
<p>
<code>mygit-status</code> shows the status of files in
the current directory, the index, and the repository.
<p>
There are many different cases to consider for <code>mygit-status</code>.
<br />
You will need to experiment with the reference implementation to find them all.

<h3>Subset 1 examples</h3>


    
        
    
<pre is="tty" style="max-height: 32em; overflow: auto;" ><kbd is="sh">./mygit-init</kbd>
Initialized empty mygit repository in .mygit
<kbd is="sh">touch a b c d e f g h</kbd>
<kbd is="sh">./mygit-add a b c d e f</kbd>
<kbd is="sh">./mygit-commit -m 'first commit'</kbd>
Committed as commit 0
<kbd is="sh">echo hello &gt;a</kbd>
<kbd is="sh">echo hello &gt;b</kbd>
<kbd is="sh">./mygit-commit -a -m 'second commit'</kbd>
Committed as commit 1
<kbd is="sh">echo world &gt;&gt;a</kbd>
<kbd is="sh">echo world &gt;&gt;b</kbd>
<kbd is="sh">echo hello world &gt;c</kbd>
<kbd is="sh">./mygit-add a</kbd>
<kbd is="sh">echo world &gt;&gt;b</kbd>
<kbd is="sh">rm d</kbd>
<kbd is="sh">./mygit-rm e</kbd>
<kbd is="sh">./mygit-add g</kbd>
<kbd is="sh">./mygit-status</kbd>
a - file changed, changes staged for commit
b - file changed, changes not staged for commit
c - file changed, changes not staged for commit
d - file deleted
e - file deleted, deleted from index
f - same as repo
g - added to index
h - untracked
mygit-add - untracked
mygit-branch - untracked
mygit-checkout - untracked
mygit-commit - untracked
mygit-init - untracked
mygit-log - untracked
mygit-merge - untracked
mygit-rm - untracked
mygit-show - untracked
mygit-status - untracked
mygit.py - untracked
</pre>


<h3>Subset 2</h3>
<p>
Subset 2 is extremely difficult.
You will need to spend considerable time
understanding the semantics of these operations
by running the reference implementation
and/or researching the equivalent Git operations.

<p>
Note the assessment scheme recognises this difficulty.
You can obtain a <b>DN</b> for the assignment without completing subset 2.


<h4>mygit-branch <var>[-d]</var> <var>[branch-name]</var></h4>
<p>
<code>mygit-branch</code> either
creates a branch,
deletes a branch, or
lists current branch names.
<p>
If <var>branch-name</var> is omitted, the names of all branches are listed.
<p>
If <var>branch-name</var> is specified,
then a branch with that name is created or deleted,<br />
depending on whether the <code>-d</code> option is specified.


<h4>mygit-checkout <var>branch-name</var></h4>
<p>
<code>mygit-checkout</code> switches branches.

<p>
Note that, unlike Git,
you can not specify a commit or a file:
you can only specify a branch.


<h4>mygit-merge (<var>branch-name</var>|<var>commit-number</var>) -m <var>message</var></h4>
<p>
<code>mygit-merge</code>
adds the changes that have been made to
the specified branch or commit
to the index, and commits them.
<p>
<code>mygit-merge</code> has complex semantics which will need careful research to implement successfully.

<h3>Subset 2 examples</h3>


    
        
    
<pre is="tty" style="max-height: 32em; overflow: auto;" ><kbd is="sh">./mygit-init</kbd>
Initialized empty mygit repository in .mygit
<kbd is="sh">seq 1 7 &gt;7.txt</kbd>
<kbd is="sh">./mygit-add 7.txt</kbd>
<kbd is="sh">./mygit-commit -m commit-1</kbd>
Committed as commit 0
<kbd is="sh">./mygit-branch b1</kbd>
<kbd is="sh">./mygit-checkout b1</kbd>
Switched to branch 'b1'
<kbd is="sh">sed -Ei 's/2/42/' 7.txt</kbd>
<kbd is="sh">cat 7.txt</kbd>
1
42
3
4
5
6
7
<kbd is="sh">./mygit-commit -a -m commit-2</kbd>
Committed as commit 1
<kbd is="sh">./mygit-checkout trunk</kbd>
Switched to branch 'trunk'
<kbd is="sh">cat 7.txt</kbd>
1
2
3
4
5
6
7
<kbd is="sh">./mygit-merge b1 -m merge-message</kbd>
Fast-forward: no commit created
<kbd is="sh">cat 7.txt</kbd>
1
42
3
4
5
6
7
</pre>

<p>
If a file has been changed in both branches
<code>mygit-merge</code> produces an error message.
<p>
Note: if a file has been changed in both branches <code>git</code> examines
which lines have been changed and combines the changes if possible.
MyGit doe not do this, for example:



    
        
    
<pre is="tty" style="max-height: 32em; overflow: auto;" ><kbd is="sh">./mygit-init</kbd>
Initialized empty mygit repository in .mygit
<kbd is="sh">seq 1 7 &gt;7.txt</kbd>
<kbd is="sh">./mygit-add 7.txt</kbd>
<kbd is="sh">./mygit-commit -m commit-1</kbd>
Committed as commit 0
<kbd is="sh">./mygit-branch b1</kbd>
<kbd is="sh">./mygit-checkout b1</kbd>
Switched to branch 'b1'
<kbd is="sh">sed -Ei 's/2/42/' 7.txt</kbd>
<kbd is="sh">cat 7.txt</kbd>
1
42
3
4
5
6
7
<kbd is="sh">./mygit-commit -a -m commit-2</kbd>
Committed as commit 1
<kbd is="sh">./mygit-checkout trunk</kbd>
Switched to branch 'trunk'
<kbd is="sh">cat 7.txt</kbd>
1
2
3
4
5
6
7
<kbd is="sh">sed -Ei 's/5/24/' 7.txt</kbd>
<kbd is="sh">cat 7.txt</kbd>
1
2
3
4
24
6
7
<kbd is="sh">./mygit-commit -a -m commit-3</kbd>
Committed as commit 2
<kbd is="sh">./mygit-merge b1 -m merge-message</kbd>
mygit-merge: error: These files can not be merged:
7.txt
<kbd is="sh">cat 7.txt</kbd>
1
2
3
4
24
6
7
</pre>

<h2>Testing</h2>

<h3>Autotests</h3>
<p>
As usual, some autotests will be available:

<pre is="tty">
<kbd is="sh">2041 autotest mygit mygit-*</kbd>
...
</pre>

<p>
You can also run only tests for a particular subset or an individual test:

<pre is="tty">
<kbd is="sh">2041 autotest mygit subset1 mygit-*</kbd>
...
<kbd is="sh">2041 autotest mygit subset1_13 mygit-*</kbd>
...
</pre>

<p>
If you are using extra Python files,
include them on the autotest command line.

<p>
Autotest and automarking will run your scripts
with a current working directory
different to the directory containing the script.
The directory containing your submission will be in <code>$PATH</code>.

<p>
You will need to do most of the testing yourself.


<h3>Test Scripts</h3>
<p>
You should submit ten POSIX-compatible Shell scripts ,
named <code>test0.sh</code> to <code>test9.sh</code>,
which run mygit commands that test an aspect of MyGit.

<p>
The <code>test.sh</code> scripts
do not have to be examples
that your program implements successfully.
but they must be your own creation.

<p>
The test scripts should show
how you've thought about testing carefully.
<p>
You are only expected to write test scripts testing  parts of  MyGit
you have attempted to implement. For example, if you have not attempted subset 2 you
are not expected to write test scripts testing mygit-merge .
<p>
We won't actually execute your test scripts.
<p>
The marking of test scripts will focus on the testing they do
rather than their style.
<p>
We are expecting test script of 20-100 lines in size.
<p>

<aside class="hint">
    <p>
    Your test script might run a series of mygit commands, checking the output
    after each one. If any output is incorrect, it might stop and indicate which
    command failed; otherwise, it might print a message saying all tests passed.
    <p>
</aside>


<h2>Permitted Languages</h2>
<p>
Your  <code>mygit</code> must be written entirely in Python.
<p>
Start <code>mygit-*</code> scripts with:

<pre class="program">
#!/usr/bin/env python3
</pre>
<p>
Your test scripts must be written entirely in POSIX compatible shell.
<p>
Start your <code>test?.sh</code> scripts with:

<pre class="program">
#!/usr/bin/env dash
</pre>


<aside class="note">
    <ul class="list-unstyled">
        <li>
            <p>
                Your <code>mygit</code> scripts must be Python only.
                <br />
                You can not use other languages such as Shell, Perl or C.
            </p>
        </li>
        <li>
            <p>
              You may <b>not</b> run external programs, e.g. via the <code>subprocess</code> module, <code>os.system</code> or otherwise.
              <br />
              For example, you can't run <code>cat</code>, <code>head</code>, <code>tail</code>.
           </p>
        </li>
        <li>
            <p>
                You may <b>not</b> use the builtin functions <code>eval</code> or <code>exec</code> functions.
                <br />
                You may <b>not</b> use (import) the <code>importlib</code> and <code>subprocess</code> modules.
            </p>
         </li>
        <li>
            <p>
                You are permitted to use any builtin function, except <code>eval</code> or <code>exec</code>.
                <br />
                You may use (import) any standard library module, except <code>importlib</code> and <code>subprocess</code>.
            </p>
        </li>
        <li>
            <p>
                You are not permitted to install Python modules with <code>pip</code> or similar software.
            </p>
        </li>
        <li>
            <p>
                Your Python code should work with the Python (/usr/bin/python3) installed on CSE servers.
            </p>
        </li>
        <li>
            <p>
                You may submit extra Python files.
            </p>
        </li>
    </ul>
</aside>


<h2>Assumptions/Clarifications</h2>
<p>
Like all good programmers,
you should make as few assumptions as possible.

<p>
You can assume <code>mygit</code> commands
are always run in the same directory as the repository,
and only files from that directory are added to the repository.

<p>
You can assume the directory in which <code>mygit</code> commands are run
will not contain sub-directories apart from <code>.mygit</code>.

<p>
You can assume where a branch name is expected
a string will be supplied starting with an alphanumeric character ([a-zA-Z0-9]),
and only containing alphanumeric characters plus '-' and '_'.
In addition, a branch name will not be supplied which is entirely numeric.
This allows branch names to be distinguished from commits when merging.
<p>
You can assume where a filename is expected a string will be supplied
starting with an alphanumeric character ([a-zA-Z0-9])
and  only containing alphanumeric characters,
plus '.', '-' and '_' characters.
<p>
You can assume where a commit number is expected
a string will be supplied which is a non-negative integer with no leading zeros.
It will not contain white space or any other characters except digits.

<p>
You can assume that
<code>mygit-add</code>,
<code>mygit-show</code>,
and <code>mygit-rm</code>
will be given just a filename,
not pathnames with slashes.

<p>
You do not have to consider file permissions or other file metadata.
For example,
you do not have to ensure files created by a checkout command
have the same permissions as when they were added.
<p>
You can assume, pathnames provided as arguments, if they exist, are ordinary files.
You can, for example, assume they are not a symbolic link, directory or device.

<p>
You do not have to handle concurrency.
You can assume only one instance of
any <code>mygit</code> command is running at any time.

<p>
You can assume that
only the arguments described above
are supplied to <code>mygit</code> commands.
You do not have to handle other arguments.

<p>
You should match the output streams
used by the reference implementations.
It writes error messages to stderr: so should you.

<p>
You should match the exit status
used by the reference implementation.
It exits with status 1 after an error: so should you.

<p>
You can assume the directory containing your scripts is in  <code>$PATH</code>.
<p>
You can not assume the directory containing your scripts is the same as the repo.
<p>
Your scripts are always run in the directory containing the repository.  <br>
Autotests and automarking will put your scripts (mygit-init, mygit-add, ...) in a different directory to the repository.
This may break scripts which run or source other scripts or file and assume they are in the current directory.
Autotests and automarking will add the directory containing your scripts to  <code>$PYTHONPATH</code> allowing imports to work.

<p>
You can assume arguments will be in
the position and order shown
in the usage message from the reference implementation.
Other orders and positions will not be tested.
For example, here is the usage message for <code>mygit-rm</code>:

<pre is="tty">
<kbd is="sh">2041 mygit-rm</kbd>
usage: mygit-rm [--force] [--cached] &lt;filenames>
</pre>

<p>
So, you assume that if the <code>--force</code>
or <code>--cached</code> options are present,
they come before all filenames,
and if they are both present
the <code>--force</code> option will come first.

<p>
MyGit error messages include the program name.
It is recommended you extract the program name (e.g. using <code>os.path.basename</code>) from <b><code>sys.argv[0]</code></b> however it is also
acceptable to hard-code the program name.
The automarking and style marking will accept both.

<p>
Do not use the modification time of a file to determine whether it has changed.
You must use the file contents.

<p>
You can assume the contents of <b>.mygit</b> are only modifed by your scripts.

<h2>Change Log</h2>
<dl class="row">
<dt class="col-3">
    Version 1.0<br />
    <small>(2025-07-23 10:00)</small>
  </dt>
  <dd class="col-9">
    <ul>
      <li>Initial release</li>
    </ul>
  </dd>
</dl>









<h2 id="assessment" class="mt-5">Assessment</h2>


<h3 id="assessment--testing">Testing</h2>
<p>
When you think your program is working,
you can use <code>autotest</code>
to run some simple automated tests:
<p>

<pre is="tty">
<kbd is="sh">2041 autotest mygit</kbd>
</pre>




<p>
<code>2041 autotest</code> will not test everything.<br />
Always do your own testing.
<p>
Automarking will be run by the lecturer after the submission deadline,
using a superset of tests to those <code>autotest</code> runs for you.



<h3 id="assessment--submission">Submission</h3>
<p>
When you are finished working on the assignment,
you must submit your work by running <code>give</code>:


<pre is="tty">
<kbd is="sh">give cs2041 ass2_mygit mygit-* test?.sh <var>[any-other-files]</var></kbd>
</pre>


<p>
You must run <code>give</code> before <strong>Week 11 Monday 10:00:00 AM 2025</strong>
to obtain the marks for this assignment.
Note that this is an individual exercise,
the work you submit with <code>give</code> must be entirely your own.

<p>
You can run <code>give</code> multiple times.<br />
Only your last submission will be marked.

<p>
If you are working at home, you may find it more convenient
to upload your work via
<a href="https://cgi.cse.unsw.edu.au/~give/Student/give.php">give's web interface</a>.

<p>
You <em>cannot</em> obtain marks by emailing your code to tutors or lecturers.

<p>
You can check your latest submission on CSE servers with:
<pre is="tty">
<kbd is="sh">2041 classrun check ass2_mygit</kbd>
</pre>

<p>
You can check the files you have submitted <a href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/student/">here</a>.

<p>
Manual marking will be done by your tutor,
who will mark for style and readability,
as described in the <strong>Assessment</strong> section below.
After your tutor has assessed your work,
you can <a href="https://cgi.cse.unsw.edu.au/~cs2041/25T2/student/">view your results here</a>;
The resulting mark will also be available
<a href="https://cgi.cse.unsw.edu.au/~give/Student/sturec.php">via give's web interface</a>.




<h3 id="assessment--due-date" class="mt-5">Due Date</h3>
<p>
This assignment is due <strong>Week 11 Monday 10:00:00 AM 2025</strong> (2025-08-11 10:00:00).
<p>
The UNSW standard late penalty for assessment is 5% per day for 5 days - this is implemented hourly for this assignment.
<p>
Your assignment mark will be reduced by 0.2% for each hour (or part thereof) late past the submission deadline.
<p>
For example,
if an assignment worth 60% was submitted half an hour late,
it would be awarded 59.8%, whereas if it was submitted past 10 hours late,
it would be awarded 57.8%.
<p>
Beware - submissions 5 or more days late will receive zero marks.
This again is the UNSW standard assessment policy.



<h2>Assessment Scheme</h2>

<p>
This assignment will contribute 15 marks
to your final COMP(2041|9044) mark.

<p>
15% of the marks for assignment 2 will come from hand-marking.
These marks will be awarded on the basis of
clarity, commenting, elegance and style:
in other words, you will be assessed on
how easy it is for a human to read and understand your program.

<p>
5% of the marks for assignment 2
will be based on the test suite you submit.

<p>
80% of the marks for assignment 2
will come from the performance of your code on a large series of tests.

<p>
An indicative assessment scheme follows.
The lecturer may vary the assessment scheme
after inspecting the assignment submissions,
but it is likely to be broadly similar to the following:


<table class="table table-sm w-75 mx-auto">
<tbody>
<tr><th scope="row">HD (85+)
    <td>All subsets working; code is beautiful; great test suite
<tr><th scope="row">DN (75+)
    <td>Subset 1 working; good clear code; good test suite
<tr><th scope="row">CR (65+)
    <td>Subset 0 working; good clear code; good test suite
<tr><th scope="row">PS (55+)
    <td>Subset 0 passing some tests; code is reasonably readable; reasonable test suite
<tr><th scope="row">PS (50+)
    <td>Good progress on assignment, but not passing autotests
<tr><th scope="row">0%
    <td>knowingly providing your work to anyone <br>
        and it is subsequently submitted (by anyone).
<tr><th scope="row">0 FL for <br> COMP(2041|9044)
    <td>submitting any other person's work; this includes joint work.
<tr><th scope="row">academic <br> misconduct
    <td>submitting another person's work without their consent;<br>
        paying another person to do work for you.
</tbody>
</table>




<h3 id="assessment--intermediate-versions-of-work" class="mt-5">Intermediate Versions of Work</h3>
<p>
You are required to submit intermediate versions of your assignment.
<p>
Every time you work on the assignment
and make some progress
you should copy your work to your CSE account
and submit it using the <code>give</code> command below.
It is fine if intermediate versions do not compile
or otherwise fail submission tests.
Only the final submitted version of your assignment will be marked.



<h3 id="assessment--attribution-of-work">Attribution of Work</h3>

<p>
This is an individual assignment.

<p>
The work you submit must be entirely your own work,
apart from any exceptions explicitly included
in the assignment specification above.
Submission of work partially or completely derived from any other person
or jointly written with any other person is not permitted.


<p>
You are only permitted to request help with the assignment
in the course forum, help sessions,
or from the teaching staff (the lecturer(s) and tutors) of COMP(2041|9044).

<p>
Do not provide or show your assignment work to any other person
(including by posting it on the forum),
apart from the teaching staff of COMP(2041|9044).
If you knowingly provide or show your assignment work
to another person for any reason,
and work derived from it is submitted,
you may be penalized,
even if that work was submitted
without your knowledge or consent;
this may apply even if your work is submitted by
a third party unknown to you.
You will not be penalized
if your work is taken
without your consent or knowledge.
<p>
Do not place your assignment work in online repositories such as github
or anywhere else that is publicly accessible.
You may use a private repository.
<p>
Submissions that violate these conditions will be penalised.
Penalties may include negative marks,
automatic failure of the course,
and possibly other academic discipline.
We are also required to report
acts of plagiarism or other student misconduct:
if students involved hold scholarships,
this may result in a loss of the scholarship.
This may also result in the loss of a student visa.

<p>
Assignment submissions will be examined,
both automatically and manually,
for such submissions.




</div> <!-- big col -->
<div class="d-none d-lg-block col-lg-2">
    <nav id="toc"></nav>
</div>

</div> <!-- big row -->

<script>
    $(function() {
    var navSelector = "#toc";
    var $myNav = $(navSelector);
    Toc.init($myNav);
    $("body").scrollspy({
      target: navSelector
    });
  });

// This code ensures the ToC will also change tabs as necessary.

window.addEventListener('hashchange', () => accessRightSection());
async function accessRightSection() {
    const id =  `#subsets_${location.hash.split(".")[0].split('#')[1]}`;
    const tabButton = document.querySelector(`a.nav-link[href="${id}"][role="tab"]`);
    if (tabButton) {
        tabButton.click();
        tabButton.scrollIntoView();
        await new Promise(r => setTimeout(r, 200));
        window.location.assign(location.hash);
    }
}
</script>




    
</main>
<footer class="mt-3 pt-3 bg-dark text-center no-print">
    <p class="text-muted">
        <strong>COMP(2041|9044) 25T2: Software Construction</strong>
        is brought to you by <br/>
        the <a href="https://www.cse.unsw.edu.au/">School of Computer Science and Engineering</a><br/>
        at the <a href="https://www.unsw.edu.au/">University of New South Wales</a>, Sydney.<br/>
        For all enquiries, please email the class account at
        <a href="mailto:@cse.unsw.edu.au"><EMAIL></a><br/>

        <small>CRICOS Provider 00098G</small>
    </p>

    
</footer>

</body>
</html>