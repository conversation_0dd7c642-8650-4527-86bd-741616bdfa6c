#!/usr/bin/env dash

echo "=== Debug checkout issue ==="

# Create clean test directory
mkdir -p debug_checkout_dir
cd debug_checkout_dir

# Reproduce scenario
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1

echo "Before checkout to trunk:"
echo "Current branch: $(cat .mygit/HEAD)"
echo "File content:"
cat 7.txt
echo "Current index:"
cat .mygit/index

python3 ../mygit-checkout trunk

echo ""
echo "After checkout to trunk:"
echo "Current branch: $(cat .mygit/HEAD)"
echo "File content:"
cat 7.txt
echo "Current index:"
cat .mygit/index

echo ""
echo "Object files:"
echo "Commit 0 index:"
cat .mygit/commits/0/index
echo "Object content for commit 0:"
object_hash=$(cat .mygit/commits/0/index | cut -d' ' -f1)
cat .mygit/objects/$object_hash

echo ""
echo "Commit 1 index:"
cat .mygit/commits/1/index
echo "Object content for commit 1:"
object_hash=$(cat .mygit/commits/1/index | cut -d' ' -f1)
cat .mygit/objects/$object_hash

echo ""
echo "=== Debug completed ==="

# Clean up
cd ..
rm -rf debug_checkout_dir
