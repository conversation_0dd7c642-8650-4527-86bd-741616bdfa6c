#!/usr/bin/env dash

echo "=== Testing subset2_30 scenario ==="

# Create clean test directory
mkdir -p test_subset2_30_dir
cd test_subset2_30_dir

# Reproduce exact subset2_30 scenario
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1
python3 ../mygit-checkout trunk

echo "File 7.txt content after checkout to trunk:"
cat 7.txt
echo ""
echo "Expected:"
seq 1 7

echo ""
echo "Debug info:"
echo "trunk branch points to commit:"
cat .mygit/branches/trunk
echo "b1 branch points to commit:"
cat .mygit/branches/b1
echo "Current index:"
cat .mygit/index

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_subset2_30_dir
