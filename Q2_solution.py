"""
Q2: k-core Based Structural Diversity Solution
COMP9312 25T2 Project

Algorithm: Efficient k-core decomposition with structural diversity computation
Time Complexity: O(m + n) for k-core decomposition, O(d^2) per vertex for diversity computation
where n = vertices, m = edges, d = maximum degree
"""

from collections import defaultdict, deque
import sys

class KCoreStructuralDiversity:
    def __init__(self, graph):
        """
        Initialize with graph representation
        
        Args:
            graph: Dictionary {vertex: [neighbors]} or edge list
        """
        self.graph = self._build_adjacency_list(graph)
        self.vertices = set(self.graph.keys())
        self.core_numbers = {}
        self._compute_core_decomposition()
    
    def _build_adjacency_list(self, graph):
        """Convert graph to adjacency list format"""
        if isinstance(graph, dict):
            return graph
        
        # Assume edge list format
        adj_list = defaultdict(set)
        for u, v in graph:
            adj_list[u].add(v)
            adj_list[v].add(u)
        
        # Convert sets to lists
        return {v: list(neighbors) for v, neighbors in adj_list.items()}
    
    def _compute_core_decomposition(self):
        """
        Compute k-core decomposition using efficient algorithm
        Time Complexity: O(m + n)
        """
        # Initialize degrees
        degrees = {v: len(self.graph.get(v, [])) for v in self.vertices}
        
        # Create degree buckets
        max_degree = max(degrees.values()) if degrees else 0
        buckets = [[] for _ in range(max_degree + 1)]
        
        # Place vertices in buckets by degree
        for v in self.vertices:
            buckets[degrees[v]].append(v)
        
        # Process vertices in order of increasing degree
        processed = set()
        self.core_numbers = {}
        
        for k in range(max_degree + 1):
            while buckets[k]:
                v = buckets[k].pop()
                if v in processed:
                    continue
                
                processed.add(v)
                self.core_numbers[v] = k
                
                # Update neighbors
                for u in self.graph.get(v, []):
                    if u not in processed and degrees[u] > k:
                        # Move u to lower bucket
                        old_degree = degrees[u]
                        degrees[u] = max(k, degrees[u] - 1)
                        
                        if degrees[u] < old_degree:
                            buckets[degrees[u]].append(u)
    
    def get_k_cores(self, subgraph_vertices, k):
        """
        Find all k-cores in the induced subgraph
        
        Args:
            subgraph_vertices: Set of vertices forming the subgraph
            k: Core number threshold
            
        Returns:
            List of k-core components (each as a set of vertices)
        """
        if not subgraph_vertices:
            return []
        
        # Build induced subgraph
        induced_graph = {}
        for v in subgraph_vertices:
            induced_graph[v] = []
            for u in self.graph.get(v, []):
                if u in subgraph_vertices:
                    induced_graph[v].append(u)
        
        # Compute k-core of induced subgraph
        degrees = {v: len(induced_graph[v]) for v in subgraph_vertices}
        remaining = set(subgraph_vertices)
        
        # Remove vertices with degree < k iteratively
        changed = True
        while changed:
            changed = False
            to_remove = []
            
            for v in remaining:
                if degrees[v] < k:
                    to_remove.append(v)
            
            for v in to_remove:
                remaining.remove(v)
                changed = True
                
                # Update neighbors' degrees
                for u in induced_graph[v]:
                    if u in remaining:
                        degrees[u] -= 1
        
        if not remaining:
            return []
        
        # Find connected components in remaining vertices
        components = []
        visited = set()
        
        for v in remaining:
            if v not in visited:
                component = set()
                queue = deque([v])
                
                while queue:
                    current = queue.popleft()
                    if current in visited:
                        continue
                    
                    visited.add(current)
                    component.add(current)
                    
                    for neighbor in induced_graph[current]:
                        if neighbor in remaining and neighbor not in visited:
                            queue.append(neighbor)
                
                if component:
                    components.append(component)
        
        return components
    
    def compute_k_core_structural_diversity(self, vertex, k):
        """
        Compute k-core based structural diversity for a vertex
        
        Args:
            vertex: Target vertex
            k: Core number threshold
            
        Returns:
            Number of k-cores in the neighbor-induced subgraph
        """
        if vertex not in self.graph:
            return 0
        
        # Get neighbors of the vertex
        neighbors = set(self.graph[vertex])
        
        if not neighbors:
            return 0
        
        # Find k-cores in neighbor-induced subgraph
        k_cores = self.get_k_cores(neighbors, k)
        
        return len(k_cores)
    
    def compute_all_structural_diversities(self, k):
        """
        Compute k-core structural diversity for all vertices
        
        Args:
            k: Core number threshold
            
        Returns:
            Dictionary {vertex: diversity_value}
        """
        diversities = {}
        
        for vertex in self.vertices:
            diversities[vertex] = self.compute_k_core_structural_diversity(vertex, k)
        
        return diversities
    
    def get_core_number(self, vertex):
        """Get the core number of a vertex"""
        return self.core_numbers.get(vertex, 0)
    
    def get_vertices_in_k_core(self, k):
        """Get all vertices with core number >= k"""
        return {v for v, core_num in self.core_numbers.items() if core_num >= k}


def example_usage():
    """Example usage of the k-core structural diversity algorithm"""
    
    # Example graph from the problem description
    edges = [
        (1, 2), (1, 3), (1, 4),
        (2, 3), (2, 5),
        (3, 4), (3, 5), (3, 6),
        (4, 6),
        (5, 6), (5, 7),
        (6, 7), (6, 8),
        (7, 8)
    ]
    
    # Create k-core structural diversity analyzer
    analyzer = KCoreStructuralDiversity(edges)
    
    print("Core numbers:")
    for vertex in sorted(analyzer.vertices):
        core_num = analyzer.get_core_number(vertex)
        print(f"Vertex {vertex}: core number = {core_num}")
    
    print("\nk-core structural diversities (k=2):")
    diversities = analyzer.compute_all_structural_diversities(k=2)
    for vertex in sorted(diversities.keys()):
        diversity = diversities[vertex]
        print(f"Vertex {vertex}: τ_2({vertex}) = {diversity}")
    
    print("\nk-core structural diversities (k=1):")
    diversities_k1 = analyzer.compute_all_structural_diversities(k=1)
    for vertex in sorted(diversities_k1.keys()):
        diversity = diversities_k1[vertex]
        print(f"Vertex {vertex}: τ_1({vertex}) = {diversity}")


class OnlineKCoreStructuralDiversity:
    """
    Online version for dynamic graph updates
    """
    def __init__(self):
        self.analyzer = None
    
    def initialize_graph(self, graph):
        """Initialize with a graph"""
        self.analyzer = KCoreStructuralDiversity(graph)
    
    def query_diversity(self, vertex, k):
        """Query k-core structural diversity for a vertex"""
        if not self.analyzer:
            return 0
        return self.analyzer.compute_k_core_structural_diversity(vertex, k)
    
    def add_edge(self, u, v):
        """Add an edge and update the analysis (simplified version)"""
        if not self.analyzer:
            return
        
        # Add edge to graph
        if u not in self.analyzer.graph:
            self.analyzer.graph[u] = []
        if v not in self.analyzer.graph:
            self.analyzer.graph[v] = []
        
        if v not in self.analyzer.graph[u]:
            self.analyzer.graph[u].append(v)
        if u not in self.analyzer.graph[v]:
            self.analyzer.graph[v].append(u)
        
        # Update vertices set
        self.analyzer.vertices.add(u)
        self.analyzer.vertices.add(v)
        
        # Recompute core decomposition (can be optimized)
        self.analyzer._compute_core_decomposition()


if __name__ == "__main__":
    example_usage()
