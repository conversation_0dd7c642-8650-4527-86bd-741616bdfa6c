class kCoreBaseStructuralDiversity(object):
    def __init__(self):
        pass

    @staticmethod
    def process(G, k):
        """
        Parameters
        ----------
        G : UndirectedUnweightedGraph
        k : int
        Returns
        -------
        List[int]  # τ_k(v) for all v
        
        Time Complexity Analysis:
        - k-core decomposition: O(m + n) using bucket-based algorithm
        - For each vertex, compute structural diversity: O(d^2) where d is degree
        - Total: O(m + n + sum(d_v^2)) where d_v is degree of vertex v
        
        Algorithm:
        1. Compute k-core decomposition of the entire graph
        2. For each vertex v, extract neighbor-induced subgraph
        3. Find k-cores in the neighbor-induced subgraph
        4. Count connected k-core components
        """
        # Handle empty graph
        if not hasattr(G, 'nodes') or len(G.nodes()) == 0:
            return []
        
        # Get all vertices and build adjacency representation
        vertices = list(G.nodes())
        vertex_to_idx = {v: i for i, v in enumerate(vertices)}
        n = len(vertices)
        
        # Build adjacency list representation (no imports allowed)
        adj_list = {}
        for v in vertices:
            adj_list[v] = []
            for neighbor in G.neighbors(v):
                adj_list[v].append(neighbor)
        
        # Initialize result array
        result = [0] * n
        
        # For each vertex, compute k-core structural diversity
        for i, vertex in enumerate(vertices):
            neighbors = adj_list[vertex]
            if len(neighbors) == 0:
                result[i] = 0
                continue
            
            # Build neighbor-induced subgraph
            neighbor_set = set(neighbors)
            neighbor_adj = {}
            for u in neighbors:
                neighbor_adj[u] = []
                for w in adj_list[u]:
                    if w in neighbor_set:
                        neighbor_adj[u].append(w)
            
            # Find k-cores in neighbor-induced subgraph
            k_cores = kCoreBaseStructuralDiversity._find_k_cores(neighbor_adj, k)
            result[i] = len(k_cores)
        
        return result
    
    @staticmethod
    def _find_k_cores(adj_list, k):
        """
        Find all k-core connected components in the given graph
        Returns list of k-core components (each as a set of vertices)
        """
        if not adj_list:
            return []
        
        vertices = list(adj_list.keys())
        if not vertices:
            return []
        
        # Compute degrees
        degrees = {}
        for v in vertices:
            degrees[v] = len(adj_list[v])
        
        # Iteratively remove vertices with degree < k
        remaining = set(vertices)
        changed = True
        
        while changed:
            changed = False
            to_remove = []
            
            for v in remaining:
                if degrees[v] < k:
                    to_remove.append(v)
            
            for v in to_remove:
                remaining.remove(v)
                changed = True
                
                # Update neighbors' degrees
                for u in adj_list[v]:
                    if u in remaining:
                        degrees[u] -= 1
        
        if not remaining:
            return []
        
        # Find connected components in remaining vertices using DFS
        components = []
        visited = set()
        
        for v in remaining:
            if v not in visited:
                component = set()
                stack = [v]  # Use list as stack (no collections.deque)
                
                while stack:
                    current = stack.pop()
                    if current in visited:
                        continue
                    
                    visited.add(current)
                    component.add(current)
                    
                    # Add unvisited neighbors in remaining set
                    for neighbor in adj_list[current]:
                        if neighbor in remaining and neighbor not in visited:
                            stack.append(neighbor)
                
                if component:
                    components.append(component)
        
        return components


# Test function (for development only, not part of submission)
def test_k_core_diversity():
    """Test function - not part of the submission"""
    
    # Mock graph class for testing
    class MockGraph:
        def __init__(self, edges):
            self._nodes = set()
            self._adj = {}
            
            for u, v in edges:
                self._nodes.add(u)
                self._nodes.add(v)
                
                if u not in self._adj:
                    self._adj[u] = set()
                if v not in self._adj:
                    self._adj[v] = set()
                
                self._adj[u].add(v)
                self._adj[v].add(u)
        
        def nodes(self):
            return list(self._nodes)
        
        def neighbors(self, v):
            return list(self._adj.get(v, []))
    
    # Test case
    edges = [
        (1, 2), (1, 3), (1, 4),
        (2, 3), (2, 5),
        (3, 4), (3, 5), (3, 6),
        (4, 6),
        (5, 6), (5, 7),
        (6, 7), (6, 8),
        (7, 8)
    ]
    
    G = MockGraph(edges)
    
    # Test k=1 and k=2
    for k in [1, 2]:
        result = kCoreBaseStructuralDiversity.process(G, k)
        print(f"k={k}: {result}")
        
        vertices = G.nodes()
        for i, v in enumerate(vertices):
            print(f"  Vertex {v}: τ_{k}({v}) = {result[i]}")
        print()


# Additional helper methods for complex cases
class kCoreBaseStructuralDiversity(kCoreBaseStructuralDiversity):
    
    @staticmethod
    def _compute_core_numbers(adj_list):
        """
        Compute core numbers for all vertices using bucket-based algorithm
        Time Complexity: O(m + n)
        """
        if not adj_list:
            return {}
        
        vertices = list(adj_list.keys())
        degrees = {v: len(adj_list[v]) for v in vertices}
        
        # Create degree buckets (using lists, no collections)
        max_degree = max(degrees.values()) if degrees else 0
        buckets = [[] for _ in range(max_degree + 1)]
        
        # Place vertices in buckets by degree
        for v in vertices:
            buckets[degrees[v]].append(v)
        
        # Process vertices in order of increasing degree
        processed = set()
        core_numbers = {}
        
        for k in range(max_degree + 1):
            while buckets[k]:
                v = buckets[k].pop()
                if v in processed:
                    continue
                
                processed.add(v)
                core_numbers[v] = k
                
                # Update neighbors' degrees
                for u in adj_list[v]:
                    if u not in processed and degrees[u] > k:
                        old_degree = degrees[u]
                        degrees[u] = max(k, degrees[u] - 1)
                        
                        if degrees[u] < old_degree:
                            buckets[degrees[u]].append(u)
        
        return core_numbers
    
    @staticmethod
    def _bfs_components(adj_list, vertices):
        """
        Find connected components using BFS (no collections.deque)
        """
        components = []
        visited = set()
        
        for start in vertices:
            if start not in visited:
                component = set()
                queue = [start]  # Use list as queue
                queue_idx = 0
                
                while queue_idx < len(queue):
                    current = queue[queue_idx]
                    queue_idx += 1
                    
                    if current in visited:
                        continue
                    
                    visited.add(current)
                    component.add(current)
                    
                    for neighbor in adj_list.get(current, []):
                        if neighbor in vertices and neighbor not in visited:
                            queue.append(neighbor)
                
                if component:
                    components.append(component)
        
        return components


if __name__ == "__main__":
    test_k_core_diversity()
