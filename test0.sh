#!/usr/bin/env dash
echo "=== Test 0: mygit-init ==="
rm -rf .mygit
echo "Test 1: Initialize new repository"
python3 mygit-init
if [ $? -eq 0 ]; then
    echo "mygit-init succeeded"
else
    echo "mygit-init failed"
    exit 1
fi
if [ -d .mygit ]; then
    echo ".mygit directory created"
else
    echo ".mygit directory not created"
    exit 1
fi
echo "Test 2: Try to initialize existing repository"
python3 mygit-init 2>/dev/null
if [ $? -ne 0 ]; then
    echo "mygit-init correctly failed on existing repository"
else
    echo "mygit-init should have failed on existing repository"
    exit 1
fi
echo "=== Test 0 PASSED ==="
