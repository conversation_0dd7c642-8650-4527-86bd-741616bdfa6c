#!/usr/bin/env dash

# Test 4: mygit-status functionality

echo "=== Test 4: mygit-status ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "content" > tracked.txt
python3 mygit-add tracked.txt
python3 mygit-commit -m "initial" > /dev/null

echo "new file" > untracked.txt
echo "modified" > tracked.txt

# Test status output
python3 mygit-status | grep "untracked" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ mygit-status shows untracked files"
else
    echo "✗ mygit-status failed to show untracked files"
    exit 1
fi

python3 mygit-status | grep "tracked.txt" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ mygit-status shows modified files"
else
    echo "✗ mygit-status failed to show modified files"
    exit 1
fi

echo "=== Test 4 PASSED ==="
