#!/usr/bin/env dash

echo "=== Testing new fixes ==="

# Create clean test directory
mkdir -p test_new_fixes_dir
cd test_new_fixes_dir

echo "1. Testing subset1_18 (rm --cached error):"
python3 ../mygit-init
echo 1 > a
echo 2 > b
echo 3 > c
python3 ../mygit-add a b c
python3 ../mygit-commit -m "first commit"
echo 4 >> a
echo 5 >> b
echo 6 >> c
echo 7 > d
echo 8 > e
python3 ../mygit-add b c d
echo 9 > b
echo "Testing mygit-rm --cached b:"
python3 ../mygit-rm --cached b

echo ""
echo "2. Testing subset1_23 (status):"
rm -rf .mygit *
python3 ../mygit-init
echo hi > a
python3 ../mygit-add a
python3 ../mygit-commit -m message
echo hello > b
echo hola > c
python3 ../mygit-add b c
echo there >> b
rm c
python3 ../mygit-status | grep "^[abc] -"

echo ""
echo "3. Testing subset2_24 (checkout error):"
rm -rf .mygit *
python3 ../mygit-init
touch a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-checkout non-existent-branch

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_new_fixes_dir
