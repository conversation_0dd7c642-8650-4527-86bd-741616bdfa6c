#!/usr/bin/env dash

echo "=== Testing all autotest fixes ==="

# Clean up
rm -rf .mygit
rm -f a b c d e

echo "=== Test subset0_4 (error message) ==="
touch a
python3 mygit-add a
echo ""

echo "=== Test subset0_11 (nothing to commit) ==="
python3 mygit-init
echo 1 > a
python3 mygit-add a
python3 mygit-commit -m message1
touch a
python3 mygit-add a
python3 mygit-commit -m message2
echo ""

echo "=== Test subset1_15 (rm --cached) ==="
rm -rf .mygit
rm -f a b c d
python3 mygit-init
echo 1 > a
echo 2 > b
python3 mygit-add a b
python3 mygit-commit -m "first commit"
echo 3 > c
echo 4 > d
python3 mygit-add c d
python3 mygit-rm --cached a c
python3 mygit-commit -m "second commit"
python3 mygit-show 0:a
python3 mygit-show 1:a
python3 mygit-show :a
echo ""

echo "=== All tests completed ==="
