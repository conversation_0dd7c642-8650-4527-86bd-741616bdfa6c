#!/usr/bin/env dash

# Test 9: Comprehensive workflow test

echo "=== Test 9: Comprehensive workflow ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

# Create multiple files
echo "file1 content" > file1.txt
echo "file2 content" > file2.txt
python3 mygit-add file1.txt file2.txt
python3 mygit-commit -m "initial commit" > /dev/null

# Modify files
echo "modified file1" > file1.txt
python3 mygit-add file1.txt
python3 mygit-commit -m "modify file1" > /dev/null

# Test log shows both commits
COMMIT_COUNT=$(python3 mygit-log | wc -l)
if [ "$COMMIT_COUNT" -eq 2 ]; then
    echo "✓ Multiple commits logged correctly"
else
    echo "✗ Commit log incorrect"
    exit 1
fi

# Test show different versions
python3 mygit-show 0:file1.txt | grep "file1 content" > /dev/null
python3 mygit-show 1:file1.txt | grep "modified file1" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ File versions preserved correctly"
else
    echo "✗ File versions not preserved"
    exit 1
fi

echo "=== Test 9 PASSED ==="
