#!/usr/bin/env dash

echo "=== Testing more checkout scenarios ==="

# Create clean test directory
mkdir -p test_more_checkout_dir
cd test_more_checkout_dir

echo "1. Testing subset2_27 (checkout rm):"
python3 ../mygit-init
touch a b c
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-add b c
python3 ../mygit-commit -m commit-1
python3 ../mygit-checkout b1
echo "Files after checkout to b1:"
ls -la | grep -E "^-.*[abc]$" || echo "No files a, b, c found"

echo ""
echo "2. Testing subset2_29 (checkout with work that would be over-written):"
rm -rf .mygit *
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch b1
echo world > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-B
echo universe > a
python3 ../mygit-checkout b1
echo "File a content after checkout:"
cat a
echo "Expected: hello"

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_more_checkout_dir
