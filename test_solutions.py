"""
测试Q1和Q2解决方案
"""

def test_q1():
    """测试Q1 - Top-k最短简单路径"""
    print("🧪 测试Q1: Top-k最短简单路径")
    print("=" * 50)
    
    try:
        from Q1_solution import KShortestPathsQuery
        
        # 简单测试图
        G = {
            's': [('a', 1), ('g', 10)],
            'a': [('b', 2)],
            'b': [('t', 3)],
            'g': [('t', 5)]
        }
        
        query = KShortestPathsQuery()
        
        # 测试k=1
        result1 = query.query(G, 's', 't', 1)
        print(f"k=1: {result1}")
        
        # 测试k=2  
        result2 = query.query(G, 's', 't', 2)
        print(f"k=2: {result2}")
        
        # 测试无路径情况
        result3 = query.query(G, 's', 'x', 1)
        print(f"无路径: {result3}")
        
        print("✅ Q1测试通过")
        
    except Exception as e:
        print(f"❌ Q1测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_q2():
    """测试Q2 - k-core结构多样性"""
    print("\n🧪 测试Q2: k-core结构多样性")
    print("=" * 50)
    
    try:
        from Q2_solution import KCoreStructuralDiversity
        
        # 简单测试图
        edges = [
            (1, 2), (1, 3),
            (2, 3), (2, 4),
            (3, 4), (4, 5)
        ]
        
        analyzer = KCoreStructuralDiversity(edges)
        
        # 测试core numbers
        print("Core numbers:")
        for v in sorted(analyzer.vertices):
            core_num = analyzer.get_core_number(v)
            print(f"  顶点{v}: core={core_num}")
        
        # 测试结构多样性
        print("\n1-core结构多样性:")
        diversities = analyzer.compute_all_structural_diversities(k=1)
        for v in sorted(diversities.keys()):
            print(f"  顶点{v}: τ_1({v}) = {diversities[v]}")
        
        print("\n2-core结构多样性:")
        diversities2 = analyzer.compute_all_structural_diversities(k=2)
        for v in sorted(diversities2.keys()):
            print(f"  顶点{v}: τ_2({v}) = {diversities2[v]}")
        
        print("✅ Q2测试通过")
        
    except Exception as e:
        print(f"❌ Q2测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_dijkstra():
    """测试Dijkstra算法"""
    print("\n🧪 测试Dijkstra算法")
    print("=" * 50)
    
    try:
        from Q1_solution import KShortestPathsQuery
        
        G = {
            'A': [('B', 1), ('C', 4)],
            'B': [('C', 2), ('D', 5)],
            'C': [('D', 1)],
            'D': []
        }
        
        path, dist = KShortestPathsQuery._dijkstra(G, 'A', 'D')
        print(f"最短路径: {path}")
        print(f"最短距离: {dist}")
        
        print("✅ Dijkstra测试通过")
        
    except Exception as e:
        print(f"❌ Dijkstra测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """运行所有测试"""
    print("🚀 开始测试COMP9312项目解决方案")
    print("=" * 60)
    
    test_dijkstra()
    test_q1()
    test_q2()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")

if __name__ == "__main__":
    main()
