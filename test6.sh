#!/usr/bin/env dash
echo "=== Test 6: mygit-branch ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "content" > file.txt
python3 mygit-add file.txt
python3 mygit-commit -m "initial" > /dev/null

python3 mygit-branch feature
if [ $? -eq 0 ]; then
    echo " mygit-branch create works"
else
    echo " mygit-branch create failed"
    exit 1
fi

python3 mygit-branch | grep "trunk" > /dev/null
if [ $? -eq 0 ]; then
    echo " mygit-branch list works"
else
    echo " mygit-branch list failed"
    exit 1
fi

python3 mygit-branch -d feature
if [ $? -eq 0 ]; then
    echo " mygit-branch delete works"
else
    echo " mygit-branch delete failed"
    exit 1
fi

echo "=== Test 6 PASSED ==="
