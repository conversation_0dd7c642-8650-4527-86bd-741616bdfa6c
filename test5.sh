#!/usr/bin/env dash
echo "=== Test 5: mygit-rm ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "content" > file.txt
python3 mygit-add file.txt
python3 mygit-commit -m "initial" > /dev/null

python3 mygit-rm --cached file.txt
if [ $? -eq 0 ] && [ -f file.txt ]; then
    echo " mygit-rm --cached works"
else
    echo " mygit-rm --cached failed"
    exit 1
fi

python3 mygit-add file.txt
python3 mygit-rm file.txt
if [ $? -eq 0 ] && [ ! -f file.txt ]; then
    echo " mygit-rm works"
else
    echo " mygit-rm failed"
    exit 1
fi

echo "=== Test 5 PASSED ==="
