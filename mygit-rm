#!/usr/bin/env python3

import os
import sys
import hashlib

def error_exit(message):
    print(f"mygit-rm: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_file_hash(filename):
    if not os.path.exists(filename):
        return None
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    return hashlib.sha1(content.encode('utf-8')).hexdigest()

def get_index_entries():
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]
    return index_entries

def get_latest_commit_entries():
    # 获取当前分支
    with open('.mygit/HEAD', 'r') as f:
        current_branch = f.read().strip()
    
    # 获取最新提交号
    branch_file = f'.mygit/branches/{current_branch}'
    if not os.path.exists(branch_file):
        return {}
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        if commit_num == '-1':
            return {}
    
    # 读取最新提交的索引
    commit_index = f'.mygit/commits/{commit_num}/index'
    if not os.path.exists(commit_index):
        return {}
    
    commit_entries = {}
    with open(commit_index, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    commit_entries[parts[1]] = parts[0]
    return commit_entries

def write_index(index_entries):
    with open('.mygit/index', 'w') as f:
        for filename, file_hash in sorted(index_entries.items()):
            f.write(f"{file_hash} {filename}\n")

def remove_file(filename, force=False, cached=False):
    index_entries = get_index_entries()
    commit_entries = get_latest_commit_entries()

    # 检查文件是否在索引中
    if filename not in index_entries:
        error_exit(f"'{filename}' is not in the mygit repository")

    # 检查文件状态以防止意外丢失工作
    current_exists = os.path.exists(filename)
    current_hash = get_file_hash(filename) if current_exists else None
    index_hash = index_entries[filename]
    commit_hash = commit_entries.get(filename)

    if not force:
        if cached:
            # --cached模式：只从索引删除，不删除工作目录文件
            # 检查索引和仓库是否不同，以及工作目录和仓库是否不同
            if commit_hash is not None and current_exists:
                if current_hash != commit_hash and index_hash != commit_hash:
                    error_exit(f"'{filename}' in index is different to both the working file and the repository")
        else:
            # 普通rm模式：从索引和工作目录都删除
            if commit_hash is None:
                # 文件不在仓库中，只在索引中
                error_exit(f"'{filename}' has staged changes in the index")
            else:
                # 文件在仓库中
                if current_exists:
                    if current_hash != commit_hash and index_hash == commit_hash:
                        error_exit(f"'{filename}' in the repository is different to the working file")
                    elif current_hash != index_hash and index_hash != commit_hash:
                        error_exit(f"'{filename}' in index is different to both the working file and the repository")
                    elif index_hash != commit_hash:
                        error_exit(f"'{filename}' has staged changes in the index")

    # 从索引中删除
    del index_entries[filename]
    write_index(index_entries)

    # 如果不是--cached模式，也从工作目录删除
    if not cached and current_exists:
        os.remove(filename)

def main():
    check_mygit_exists()
    if len(sys.argv) < 2:
        print("usage: mygit-rm [--force] [--cached] <filenames>", file=sys.stderr)
        sys.exit(1)
    
    force = False
    cached = False
    filenames = []
    
    i = 1
    while i < len(sys.argv):
        if sys.argv[i] == '--force':
            force = True
        elif sys.argv[i] == '--cached':
            cached = True
        else:
            filenames.append(sys.argv[i])
        i += 1
    
    if not filenames:
        error_exit("nothing specified, nothing removed")
    
    for filename in filenames:
        remove_file(filename, force, cached)

if __name__ == '__main__':
    main()
