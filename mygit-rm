#!/usr/bin/env python3

import os
import sys
import hashlib

def error_exit(message):
    """打印错误信息并退出"""
    print(f"mygit-rm: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    """检查是否在mygit仓库中"""
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_file_hash(filename):
    """计算文件的哈希值"""
    if not os.path.exists(filename):
        return None
    with open(filename, 'rb') as f:
        content = f.read()
    return hashlib.sha1(content).hexdigest()

def get_index_entries():
    """获取索引中的所有条目"""
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]
    return index_entries

def get_latest_commit_entries():
    """获取最新提交中的文件条目"""
    # 获取当前分支
    with open('.mygit/HEAD', 'r') as f:
        current_branch = f.read().strip()
    
    # 获取最新提交号
    branch_file = f'.mygit/branches/{current_branch}'
    if not os.path.exists(branch_file):
        return {}
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        if commit_num == '-1':
            return {}
    
    # 读取最新提交的索引
    commit_index = f'.mygit/commits/{commit_num}/index'
    if not os.path.exists(commit_index):
        return {}
    
    commit_entries = {}
    with open(commit_index, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    commit_entries[parts[1]] = parts[0]
    return commit_entries

def write_index(index_entries):
    """写入索引文件"""
    with open('.mygit/index', 'w') as f:
        for filename, file_hash in sorted(index_entries.items()):
            f.write(f"{file_hash} {filename}\n")

def remove_file(filename, force=False, cached=False):
    """删除文件"""
    index_entries = get_index_entries()
    commit_entries = get_latest_commit_entries()
    
    # 检查文件是否在索引中
    if filename not in index_entries:
        error_exit(f"'{filename}' is not in the mygit repository")
    
    # 检查文件状态以防止意外丢失工作
    current_exists = os.path.exists(filename)
    current_hash = get_file_hash(filename) if current_exists else None
    index_hash = index_entries[filename]
    commit_hash = commit_entries.get(filename)
    
    if not force:
        # 检查是否会丢失工作
        if current_exists and current_hash != index_hash:
            error_exit(f"'{filename}' in index is different to both working file and repository")
        
        if index_hash != commit_hash and current_exists:
            error_exit(f"'{filename}' has changes staged in the index")
    
    # 从索引中删除
    del index_entries[filename]
    write_index(index_entries)
    
    # 如果不是--cached模式，也从工作目录删除
    if not cached and current_exists:
        os.remove(filename)

def main():
    check_mygit_exists()
    
    if len(sys.argv) < 2:
        print("usage: mygit-rm [--force] [--cached] <filenames>", file=sys.stderr)
        sys.exit(1)
    
    force = False
    cached = False
    filenames = []
    
    i = 1
    while i < len(sys.argv):
        if sys.argv[i] == '--force':
            force = True
        elif sys.argv[i] == '--cached':
            cached = True
        else:
            filenames.append(sys.argv[i])
        i += 1
    
    if not filenames:
        error_exit("nothing specified, nothing removed")
    
    for filename in filenames:
        remove_file(filename, force, cached)

if __name__ == '__main__':
    main()
