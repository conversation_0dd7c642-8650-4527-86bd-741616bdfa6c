#!/usr/bin/env dash
echo "=== Test 8: mygit-merge ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "line1" > file.txt
python3 mygit-add file.txt
python3 mygit-commit -m "initial" > /dev/null

python3 mygit-branch feature
python3 mygit-checkout feature > /dev/null

echo "line2" >> file.txt
python3 mygit-commit -a -m "feature commit" > /dev/null

python3 mygit-checkout trunk > /dev/null

python3 mygit-merge feature -m "merge feature"
if [ $? -eq 0 ]; then
    echo " mygit-merge works"
else
    echo " mygit-merge failed"
    exit 1
fi


if grep -q "line1" file.txt && grep -q "line2" file.txt; then
    echo " mygit-merge content correct"
else
    echo " mygit-merge content incorrect"
    exit 1
fi

echo "=== Test 8 PASSED ==="
