#!/usr/bin/env dash

# Test 3: mygit-show functionality

echo "=== Test 3: mygit-show ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "line 1" > file.txt
python3 mygit-add file.txt
python3 mygit-commit -m "commit 1" > /dev/null

echo "line 2" >> file.txt
python3 mygit-add file.txt

# Test show from commit
python3 mygit-show 0:file.txt | grep "line 1" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ mygit-show from commit works"
else
    echo "✗ mygit-show from commit failed"
    exit 1
fi

# Test show from index
python3 mygit-show :file.txt | grep "line 2" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ mygit-show from index works"
else
    echo "✗ mygit-show from index failed"
    exit 1
fi

echo "=== Test 3 PASSED ==="
