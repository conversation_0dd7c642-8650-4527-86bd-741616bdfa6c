#!/usr/bin/env dash

echo "=== Debug subset2_29 scenario ==="

# Create clean test directory
mkdir -p debug_subset2_29_dir
cd debug_subset2_29_dir

# Reproduce scenario
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch branchA
echo world > b
python3 ../mygit-add b
python3 ../mygit-commit -m commit-B
python3 ../mygit-checkout branchA

echo "After checkout to branchA:"
echo "Current index:"
cat .mygit/index
echo "File b exists:" $(test -f b && echo "yes" || echo "no")

echo "new contents" > b
echo "After modifying file b:"
echo "File b content:"
cat b
echo "File b hash:"
echo "new contents" | sha1sum

echo "Current index hash for b:"
cat .mygit/index | grep "b$" || echo "b not in index"

echo "Target (trunk) index:"
cat .mygit/commits/1/index

echo "Now attempting checkout to trunk..."
python3 ../mygit-checkout trunk

echo ""
echo "=== Debug completed ==="

# Clean up
cd ..
rm -rf debug_subset2_29_dir
