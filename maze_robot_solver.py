import numpy as np
from scipy.optimize import linprog
import networkx as nx
import matplotlib.pyplot as plt

def solve_maze_robot_problem():
    """
    解决迷宫机器人问题 - 线性规划方法
    """
    print("🤖 迷宫机器人问题求解")
    print("=" * 50)
    
    # 定义变量顺序：x01, x12, x13, x14, x23, x24, x25, x34, x35, x45, x50
    # 目标函数系数（最小化）
    c = [0.5, 0.5, 0.25, 0.125, 0.5, 0.25, 0.125, 0.5, 0.25, 1.0, 0]
    
    # 等式约束矩阵 A_eq 和右侧向量 b_eq
    # 流量平衡约束
    A_eq = [
        # 节点0: x01 - x50 = 1
        [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1],
        # 节点1: x01 - x12 - x13 - x14 = 0  
        [1, -1, -1, -1, 0, 0, 0, 0, 0, 0, 0],
        # 节点2: x12 - x23 - x24 - x25 = 0
        [0, 1, 0, 0, -1, -1, -1, 0, 0, 0, 0],
        # 节点3: x13 + x23 - x34 - x35 = 0
        [0, 0, 1, 0, 1, 0, 0, -1, -1, 0, 0],
        # 节点4: x14 + x24 + x34 - x45 = 0
        [0, 0, 0, 1, 0, 1, 0, 1, 0, -1, 0],
        # 节点5: x25 + x35 + x45 - x50 = 1
        [0, 0, 0, 0, 0, 0, 1, 0, 1, 1, -1]
    ]
    
    b_eq = [1, 0, 0, 0, 0, 1]
    
    # 变量边界（所有变量非负）
    bounds = [(0, None) for _ in range(11)]
    
    # 求解线性规划
    result = linprog(c, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
    
    print("📊 原问题求解结果：")
    print(f"状态: {result.message}")
    print(f"最优值: {result.fun:.6f}")
    
    # 显示最优解
    var_names = ['x01', 'x12', 'x13', 'x14', 'x23', 'x24', 'x25', 'x34', 'x35', 'x45', 'x50']
    print("\n🎯 最优解（原问题）:")
    for i, (name, value) in enumerate(zip(var_names, result.x)):
        if value > 1e-6:  # 只显示非零值
            print(f"{name} = {value:.6f}")
    
    # 手动计算对偶问题
    print("\n" + "=" * 50)
    print("🔄 对偶问题分析")

    # 基于互补松弛条件和最优解分析对偶解
    print("基于原问题最优解分析对偶问题...")

    # 从原问题最优解可知：x01=1, x13=1, x35=1, x50=1（隐含）
    # 这意味着路径是 0→1→3→5→0

    # 对偶约束（对于每条边 (i,j)）：yj - yi <= cij
    # 对于最优解中使用的边，约束取等号（互补松弛）

    print("\n📐 基于互补松弛条件的对偶解:")
    print("使用的边及其对偶约束:")
    print("边 0→1: y1 - y0 = 0.5")
    print("边 1→3: y3 - y1 = 0.25")
    print("边 3→5: y5 - y3 = 0.25")
    print("边 5→0: y0 - y5 = 0 (新增边，成本为0)")

    # 解方程组（设 y0 = 0 作为基准）
    y0 = 0
    y1 = y0 + 0.5  # = 0.5
    y3 = y1 + 0.25  # = 0.75
    y5 = y3 + 0.25  # = 1.0

    # 验证 y0 - y5 = 0 的约束
    print(f"\n验证循环约束: y0 - y5 = {y0} - {y5} = {y0 - y5}")

    # 对于未使用的边，需要满足 yj - yi <= cij
    # 计算 y2 和 y4

    # 从边 1→2 (未使用): y2 - y1 <= 0.5, 所以 y2 <= 1.0
    # 从边 2→5 (未使用): y5 - y2 <= 0.125, 所以 y2 >= 0.875
    # 从边 1→4 (未使用): y4 - y1 <= 0.125, 所以 y4 <= 0.625
    # 从边 4→5 (未使用): y5 - y4 <= 1.0, 所以 y4 >= 0

    y2 = 0.875  # 取边界值
    y4 = 0.625  # 取边界值

    dual_solution = [y0, y1, y2, y3, y4, y5]
    dual_var_names = ['y0', 'y1', 'y2', 'y3', 'y4', 'y5']

    print("\n🎯 对偶最优解:")
    for name, value in zip(dual_var_names, dual_solution):
        print(f"{name} = {value:.6f}")

    # 计算对偶目标函数值
    dual_objective = sum(b_eq[i] * dual_solution[i] for i in range(len(b_eq)))

    print(f"\n✅ 强对偶性验证:")
    print(f"原问题最优值: {result.fun:.6f}")
    print(f"对偶问题最优值: {dual_objective:.6f}")
    print(f"差值: {abs(result.fun - dual_objective):.10f}")

    # 创建一个模拟的对偶结果对象
    class DualResult:
        def __init__(self, x, fun):
            self.x = x
            self.fun = fun
            self.success = True
            self.message = "手动计算得出"

    result_dual = DualResult(dual_solution, dual_objective)
    
    return result, result_dual

def visualize_solution(result):
    """可视化最优路径"""
    print("\n" + "=" * 50)
    print("🗺️ 最优路径分析")
    
    var_names = ['x01', 'x12', 'x13', 'x14', 'x23', 'x24', 'x25', 'x34', 'x35', 'x45', 'x50']
    edges = [(0,1), (1,2), (1,3), (1,4), (2,3), (2,4), (2,5), (3,4), (3,5), (4,5), (5,0)]
    
    active_edges = []
    for i, (edge, value) in enumerate(zip(edges, result.x)):
        if value > 1e-6:
            active_edges.append((edge, value))
            print(f"边 {edge[0]}→{edge[1]}: 流量 = {value:.6f}")
    
    # 找出从0到5的路径
    print("\n🛤️ 最优路径:")
    if result.x[0] > 1e-6:  # x01 > 0
        path = [0, 1]
        current = 1
        
        while current != 5:
            for i, (edge, value) in enumerate(zip(edges, result.x)):
                if edge[0] == current and value > 1e-6:
                    path.append(edge[1])
                    current = edge[1]
                    break
        
        print(f"路径: {' → '.join(map(str, path))}")
        
        # 计算路径成本
        total_cost = 0
        for i in range(len(path)-1):
            for j, edge in enumerate(edges):
                if edge == (path[i], path[i+1]):
                    cost = [0.5, 0.5, 0.25, 0.125, 0.5, 0.25, 0.125, 0.5, 0.25, 1.0, 0][j]
                    total_cost += cost
                    print(f"  {path[i]}→{path[i+1]}: 成本 {cost}")
        
        print(f"总成本: {total_cost:.6f}")

def main():
    """主函数"""
    try:
        # 求解问题
        primal_result, dual_result = solve_maze_robot_problem()
        
        # 可视化结果
        visualize_solution(primal_result)
        
        print("\n" + "=" * 50)
        print("🎉 问题求解完成！")
        
    except Exception as e:
        print(f"❌ 求解过程中出现错误: {e}")

if __name__ == "__main__":
    main()
