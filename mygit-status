#!/usr/bin/env python3

import os
import sys
import hashlib
import glob

def error_exit(message):
    """打印错误信息并退出"""
    print(f"mygit-status: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    """检查是否在mygit仓库中"""
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_file_hash(filename):
    """计算文件的哈希值"""
    if not os.path.exists(filename):
        return None
    with open(filename, 'rb') as f:
        content = f.read()
    return hashlib.sha1(content).hexdigest()

def get_index_entries():
    """获取索引中的所有条目"""
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]
    return index_entries

def get_latest_commit_entries():
    """获取最新提交中的文件条目"""
    # 获取当前分支
    with open('.mygit/HEAD', 'r') as f:
        current_branch = f.read().strip()
    
    # 获取最新提交号
    branch_file = f'.mygit/branches/{current_branch}'
    if not os.path.exists(branch_file):
        return {}
    
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
        if commit_num == '-1':
            return {}
    
    # 读取最新提交的索引
    commit_index = f'.mygit/commits/{commit_num}/index'
    if not os.path.exists(commit_index):
        return {}
    
    commit_entries = {}
    with open(commit_index, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    commit_entries[parts[1]] = parts[0]
    return commit_entries

def get_all_files():
    """获取当前目录中的所有文件"""
    files = []
    for item in os.listdir('.'):
        if os.path.isfile(item) and not item.startswith('.'):
            files.append(item)
    return files

def get_file_status(filename, index_entries, commit_entries):
    """获取文件状态"""
    current_exists = os.path.exists(filename)
    current_hash = get_file_hash(filename) if current_exists else None
    index_hash = index_entries.get(filename)
    commit_hash = commit_entries.get(filename)
    
    # 文件在仓库中但被删除
    if commit_hash and not current_exists:
        if index_hash == commit_hash:
            return "file deleted"
        elif not index_hash:
            return "file deleted, deleted from index"
        else:
            return "file deleted, different changes staged for commit"
    
    # 文件在索引中但被删除
    if index_hash and not current_exists:
        return "file deleted"
    
    # 文件不在仓库中
    if not commit_hash:
        if index_hash:
            return "added to index"
        elif current_exists:
            return "untracked"
        else:
            return None  # 不应该到达这里
    
    # 文件在仓库中
    if current_exists:
        if index_hash == commit_hash:
            if current_hash == commit_hash:
                return "same as repo"
            else:
                return "file changed, changes not staged for commit"
        else:
            if current_hash == index_hash:
                return "file changed, changes staged for commit"
            else:
                return "file changed, different changes staged for commit"
    
    return "unknown"

def main():
    check_mygit_exists()
    
    index_entries = get_index_entries()
    commit_entries = get_latest_commit_entries()
    
    # 获取所有需要检查的文件
    all_files = set()
    all_files.update(get_all_files())
    all_files.update(index_entries.keys())
    all_files.update(commit_entries.keys())
    
    # 按文件名排序
    for filename in sorted(all_files):
        status = get_file_status(filename, index_entries, commit_entries)
        if status:
            print(f"{filename} - {status}")

if __name__ == '__main__':
    main()
