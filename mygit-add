#!/usr/bin/env python3

import os
import sys
import shutil
import hashlib

def error_exit(message):
    print(f"mygit-add: error: {message}", file=sys.stderr)
    sys.exit(1)
def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("mygit repository directory .mygit not found")
def get_file_hash(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    return hashlib.sha1(content.encode('utf-8')).hexdigest()
def add_file_to_index(filename):
    # 读取当前索引
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]

    # 检查文件是否存在
    if not os.path.exists(filename):
        # 如果文件不存在但在索引中，从索引中删除
        if filename in index_entries:
            del index_entries[filename]
            # 写回索引文件
            with open('.mygit/index', 'w') as f:
                for filename_key, hash_value in sorted(index_entries.items()):
                    f.write(f"{hash_value} {filename_key}\n")
            return
        else:
            error_exit(f"can not open '{filename}'")
    # 检查是否是普通文件
    if not os.path.isfile(filename):
        error_exit(f"'{filename}' is not a regular file")
    # 计算文件哈希
    file_hash = get_file_hash(filename)
    # 将文件内容复制到objects目录
    object_path = f'.mygit/objects/{file_hash}'
    with open(filename, 'r', encoding='utf-8') as src:
        with open(object_path, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
    # 更新索引
    index_entries[filename] = file_hash
    # 写回索引文件
    with open('.mygit/index', 'w') as f:
        for filename_key, hash_value in sorted(index_entries.items()):
            f.write(f"{hash_value} {filename_key}\n")
def main():
    check_mygit_exists()
    if len(sys.argv) < 2:
        error_exit("nothing specified, nothing added")
    # 处理所有文件参数
    for filename in sys.argv[1:]:
        add_file_to_index(filename)
if __name__ == '__main__':
    main()
