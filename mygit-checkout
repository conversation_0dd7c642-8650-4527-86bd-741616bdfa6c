#!/usr/bin/env python3

import os
import sys
import shutil
import hashlib
import hashlib

def error_exit(message):
    print(f"mygit-checkout: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if os.path.exists(branch_file):
        with open(branch_file, 'r') as f:
            commit_num = f.read().strip()
            return int(commit_num) if commit_num != '-1' else -1
    return -1

def checkout_branch(branch_name):
    current_branch = get_current_branch()
    
    # 检查是否已经在目标分支
    if branch_name == current_branch:
        print(f"Already on '{branch_name}'")
        return
    
    # 检查目标分支是否存在
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        error_exit(f"unknown branch '{branch_name}'")
    
    # 获取目标分支的提交号
    with open(branch_file, 'r') as f:
        commit_num = f.read().strip()
    
    # 更新HEAD指向新分支
    with open('.mygit/HEAD', 'w') as f:
        f.write(branch_name + '\n')
    
    # 获取当前分支的提交信息
    current_branch = get_current_branch()
    current_commit = get_branch_commit(current_branch)

    # 获取当前索引和工作目录状态
    current_index = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        current_index[parts[1]] = parts[0]

    # 获取当前分支的文件状态
    current_branch_files = {}
    if current_commit != -1:
        current_commit_index = f'.mygit/commits/{current_commit}/index'
        if os.path.exists(current_commit_index):
            with open(current_commit_index, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split(' ', 1)
                        if len(parts) == 2:
                            current_branch_files[parts[1]] = parts[0]

    # 获取目标分支的文件状态
    target_files = {}
    if commit_num != '-1':
        commit_index = f'.mygit/commits/{commit_num}/index'
        if os.path.exists(commit_index):
            with open(commit_index, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split(' ', 1)
                        if len(parts) == 2:
                            target_files[parts[1]] = parts[0]

    # 检查是否有会被覆盖的修改
    conflicts = []
    for filename in set(current_index.keys()) | set(target_files.keys()):
        current_exists = os.path.exists(filename)
        if current_exists:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                current_hash = hashlib.sha1(content.encode('utf-8')).hexdigest()
            except:
                continue
        else:
            current_hash = None

        current_index_hash = current_index.get(filename)
        target_hash = target_files.get(filename)

        # 检查是否会覆盖未提交的修改
        if current_exists and target_hash:
            # 情况1：文件在索引中，工作目录有修改，目标分支内容不同
            if (current_index_hash and
                current_hash != current_index_hash and  # 工作目录有修改
                target_hash != current_index_hash):     # 目标分支内容不同
                conflicts.append(filename)
            # 情况2：文件不在索引中，但目标分支有这个文件，且内容不同
            elif (not current_index_hash and
                  target_hash != current_hash):         # 目标分支内容与工作目录不同
                conflicts.append(filename)

    if conflicts:
        error_exit(f"Your changes to the following files would be overwritten by checkout:\n" +
                  "\n".join(conflicts))

    # 更新索引到目标分支状态
    with open('.mygit/index', 'w') as f:
        for filename, file_hash in sorted(target_files.items()):
            f.write(f"{file_hash} {filename}\n")

    # 处理每个文件
    all_files = set(current_index.keys()) | set(target_files.keys()) | set(os.listdir('.') if os.path.exists('.') else [])

    for filename in all_files:
        # 跳过mygit相关文件和目录，以及测试脚本
        if (filename.startswith('.mygit') or filename.startswith('mygit-') or
            filename.endswith('.sh') or filename.endswith('.py') or
            filename.startswith('test_') or filename.startswith('debug_') or
            filename == '报错.txt' or filename == '报错20+.txt'):
            continue

        current_exists = os.path.exists(filename)
        current_hash = None
        if current_exists:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                current_hash = hashlib.sha1(content.encode('utf-8')).hexdigest()
            except:
                continue

        target_hash = target_files.get(filename)
        current_branch_hash = current_branch_files.get(filename)

        if target_hash:
            # 目标分支有这个文件
            object_path = f'.mygit/objects/{target_hash}'
            if os.path.exists(object_path):
                # 决定是否更新文件
                should_update = True

                if current_exists:
                    # 检查是否有未提交的修改
                    current_index_hash = current_index.get(filename)

                    # 如果工作目录文件与索引不同，说明有未提交的修改
                    if current_index_hash and current_hash != current_index_hash:
                        should_update = False  # 保留未提交的修改

                if should_update:
                    with open(object_path, 'r', encoding='utf-8') as src:
                        with open(filename, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
        else:
            # 目标分支没有这个文件，删除它（如果它来自当前分支）
            if current_exists and filename in current_index:
                # 删除在索引中但目标分支没有的文件
                os.remove(filename)
    
    print(f"Switched to branch '{branch_name}'")

def main():
    check_mygit_exists()
    
    if len(sys.argv) != 2:
        error_exit("usage: mygit-checkout branch-name")
    
    branch_name = sys.argv[1]
    checkout_branch(branch_name)

if __name__ == '__main__':
    main()
