#!/usr/bin/env python3

import os
import sys
import shutil

def error_exit(message):
    """打印错误信息并退出"""
    print(f"mygit-checkout: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    """检查是否在mygit仓库中"""
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    """获取当前分支名"""
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    """获取分支的提交号"""
    branch_file = f'.mygit/branches/{branch_name}'
    if os.path.exists(branch_file):
        with open(branch_file, 'r') as f:
            commit_num = f.read().strip()
            return int(commit_num) if commit_num != '-1' else -1
    return -1

def checkout_branch(branch_name):
    """切换到指定分支"""
    current_branch = get_current_branch()

    # 检查目标分支是否存在
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        error_exit(f"unknown branch '{branch_name}'")

    # 获取目标分支的提交号
    target_commit = get_branch_commit(branch_name)

    # 更新HEAD指向新分支
    with open('.mygit/HEAD', 'w') as f:
        f.write(branch_name + '\n')
    
    # 如果分支有提交，更新索引但保持工作目录状态
    if target_commit != -1:
        commit_index = f'.mygit/commits/{target_commit}/index'
        if os.path.exists(commit_index):
            # 复制提交的索引到当前索引
            shutil.copy2(commit_index, '.mygit/index')

            # 只在没有工作目录修改时更新文件
            # 这样可以保持跨分支的修改状态
            with open(commit_index, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split(' ', 1)
                        if len(parts) == 2:
                            file_hash, filename = parts
                            object_path = f'.mygit/objects/{file_hash}'
                            if os.path.exists(object_path) and not os.path.exists(filename):
                                # 只有文件不存在时才从对象恢复
                                shutil.copy2(object_path, filename)
    else:
        # 分支没有提交，清空索引
        with open('.mygit/index', 'w') as f:
            pass
    
    print(f"Switched to branch '{branch_name}'")

def main():
    check_mygit_exists()
    
    if len(sys.argv) != 2:
        error_exit("usage: mygit-checkout branch-name")
    
    branch_name = sys.argv[1]
    checkout_branch(branch_name)

if __name__ == '__main__':
    main()
