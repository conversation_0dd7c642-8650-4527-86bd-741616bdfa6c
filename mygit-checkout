#!/usr/bin/env python3

import os
import sys
import shutil
import hashlib

def error_exit(message):
    print(f"mygit-checkout: error: {message}", file=sys.stderr)
    sys.exit(1)

def check_mygit_exists():
    if not os.path.exists('.mygit'):
        error_exit("not a mygit repository")

def get_current_branch():
    with open('.mygit/HEAD', 'r') as f:
        return f.read().strip()

def get_branch_commit(branch_name):
    branch_file = f'.mygit/branches/{branch_name}'
    if os.path.exists(branch_file):
        with open(branch_file, 'r') as f:
            commit_num = f.read().strip()
            return int(commit_num) if commit_num != '-1' else -1
    return -1

def get_file_hash(filename):
    if not os.path.exists(filename):
        return None
    with open(filename, 'rb') as f:
        content = f.read()
    return hashlib.sha1(content).hexdigest()

def get_commit_entries(commit_num):
    if commit_num == -1:
        return {}
    
    commit_index = f'.mygit/commits/{commit_num}/index'
    if not os.path.exists(commit_index):
        return {}
    
    entries = {}
    with open(commit_index, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    entries[parts[1]] = parts[0]
    return entries

def get_index_entries():
    index_entries = {}
    if os.path.exists('.mygit/index'):
        with open('.mygit/index', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(' ', 1)
                    if len(parts) == 2:
                        index_entries[parts[1]] = parts[0]
    return index_entries

def check_overwrite_conflicts(target_commit):
    if target_commit == -1:
        return []
    
    target_entries = get_commit_entries(target_commit)
    index_entries = get_index_entries()
    conflicts = []
    
    for filename in target_entries:
        if os.path.exists(filename):
            current_hash = get_file_hash(filename)
            target_hash = target_entries[filename]
            index_hash = index_entries.get(filename)
            
            # 如果工作目录文件与目标不同，且不在索引中或与索引不同
            if (current_hash != target_hash and 
                (index_hash is None or current_hash != index_hash)):
                conflicts.append(filename)
    
    return conflicts

def checkout_branch(branch_name):
    current_branch = get_current_branch()
    
    # 检查目标分支是否存在
    branch_file = f'.mygit/branches/{branch_name}'
    if not os.path.exists(branch_file):
        error_exit(f"unknown branch '{branch_name}'")
    
    # 获取目标分支的提交号
    target_commit = get_branch_commit(branch_name)
    
    # 检查是否有文件会被覆盖
    conflicts = check_overwrite_conflicts(target_commit)
    if conflicts:
        print("mygit-checkout: error: Your changes to the following files would be overwritten by checkout:")
        for filename in sorted(conflicts):
            print(filename)
        return
    
    # 更新HEAD指向新分支
    with open('.mygit/HEAD', 'w') as f:
        f.write(branch_name + '\n')
    
    # 如果分支有提交，更新工作目录和索引
    if target_commit != -1:
        commit_index = f'.mygit/commits/{target_commit}/index'
        if os.path.exists(commit_index):
            # 复制提交的索引到当前索引
            shutil.copy2(commit_index, '.mygit/index')
            
            # 获取目标分支的文件
            target_entries = get_commit_entries(target_commit)
            
            # 删除不在目标分支中的文件
            current_files = set()
            for item in os.listdir('.'):
                if (os.path.isfile(item) and 
                    not item.startswith('.') and 
                    not item.startswith('mygit-') and 
                    not item.startswith('test') and
                    not item.endswith('.py') and
                    not item.endswith('.sh')):
                    current_files.add(item)
            
            for filename in current_files:
                if filename not in target_entries:
                    try:
                        os.remove(filename)
                    except:
                        pass
            
            # 更新工作目录中的文件
            for filename, file_hash in target_entries.items():
                object_path = f'.mygit/objects/{file_hash}'
                if os.path.exists(object_path):
                    shutil.copy2(object_path, filename)
    else:
        # 分支没有提交，清空索引和工作目录
        with open('.mygit/index', 'w') as f:
            pass
        
        # 删除所有跟踪的文件
        for item in os.listdir('.'):
            if (os.path.isfile(item) and 
                not item.startswith('.') and 
                not item.startswith('mygit-') and 
                not item.startswith('test') and
                not item.endswith('.py') and
                not item.endswith('.sh')):
                try:
                    os.remove(item)
                except:
                    pass
    
    print(f"Switched to branch '{branch_name}'")

def main():
    check_mygit_exists()
    
    if len(sys.argv) != 2:
        error_exit("usage: mygit-checkout branch-name")
    
    branch_name = sys.argv[1]
    checkout_branch(branch_name)

if __name__ == '__main__':
    main()
