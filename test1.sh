#!/usr/bin/env dash

# Test 1: Basic mygit-add functionality

echo "=== Test 1: mygit-add ==="

# Clean up and initialize
rm -rf .mygit test_file1.txt test_file2.txt
python3 mygit-init > /dev/null

# Create test files
echo "Hello World" > test_file1.txt
echo "Second file" > test_file2.txt

# Test 1: Add single file
echo "Test 1: Add single file"
python3 mygit-add test_file1.txt
if [ $? -eq 0 ]; then
    echo "✓ mygit-add single file succeeded"
else
    echo "✗ mygit-add single file failed"
    exit 1
fi

# Test 2: Add multiple files
echo "Test 2: Add multiple files"
python3 mygit-add test_file1.txt test_file2.txt
if [ $? -eq 0 ]; then
    echo "✓ mygit-add multiple files succeeded"
else
    echo "✗ mygit-add multiple files failed"
    exit 1
fi

# Test 3: Try to add non-existent file
echo "Test 3: Try to add non-existent file"
python3 mygit-add nonexistent.txt 2>/dev/null
if [ $? -ne 0 ]; then
    echo "✓ mygit-add correctly failed on non-existent file"
else
    echo "✗ mygit-add should have failed on non-existent file"
    exit 1
fi

# Clean up
rm -f test_file1.txt test_file2.txt

echo "=== Test 1 PASSED ==="
