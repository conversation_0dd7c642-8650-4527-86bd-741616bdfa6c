#!/usr/bin/env dash

echo "=== Testing fixes for subset1_17 ==="

# Clean up
rm -rf .mygit
rm -f a b

# Test the exact sequence from subset1_17
echo "$ mygit-init"
python3 mygit-init

echo "$ touch a b"
touch a b

echo "$ mygit-add a b"
python3 mygit-add a b

echo "$ mygit-commit -m 'first commit'"
python3 mygit-commit -m 'first commit'

echo "$ rm a"
rm a

echo "$ mygit-commit -m 'second commit'"
python3 mygit-commit -m 'second commit'

echo "$ mygit-add a"
python3 mygit-add a

echo "$ mygit-commit -m 'second commit'"
python3 mygit-commit -m 'second commit'

echo "$ mygit-rm --cached b"
python3 mygit-rm --cached b

echo "$ mygit-commit -m 'second commit'"
python3 mygit-commit -m 'second commit'

echo "$ mygit-rm b"
python3 mygit-rm b

echo "=== Test completed ==="
