import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import matplotlib

# 设置中文字体支持和后端
matplotlib.use('Agg')  # 使用非交互式后端
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# 快速测试 - 只生成一个简单的图来验证标题
def quick_test():
    # 生成一些测试数据
    data = np.random.normal(22, 0.2, 1000)
    
    plt.figure(figsize=(10, 6))
    plt.hist(data, bins=50, density=True, alpha=0.7, color='lightblue', edgecolor='navy')
    
    # 测试标题
    sheet_name = "cp"
    plt.title(f'{sheet_name}正态分布图', fontsize=18, fontweight='bold')
    plt.xlabel('数值', fontsize=14)
    plt.ylabel('频率', fontsize=14)
    plt.grid(True, alpha=0.3)
    
    # 保存测试图片
    plt.savefig('test_cp_title.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 测试图片已生成: test_cp_title.png")
    print("标题应该显示: cp正态分布图")

if __name__ == '__main__':
    quick_test()
