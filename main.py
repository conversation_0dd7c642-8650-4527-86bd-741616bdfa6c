import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
import matplotlib
import os

# 设置中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def read_excel_data(file_path):
    """
    读取Excel文件中的数据
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print("Excel文件读取成功！")
        print(f"数据形状：{df.shape}")

        # 找到有数据的列（'Unnamed: 6'列）
        data_column = 'Unnamed: 6'
        if data_column in df.columns:
            # 获取该列的数据，跳过第一行（标题）
            raw_data = df[data_column].iloc[1:]  # 跳过第一行'数据'

            # 转换为数值类型，无法转换的设为NaN
            numeric_data = pd.to_numeric(raw_data, errors='coerce')

            # 删除NaN值
            clean_data = numeric_data.dropna()

            print(f"找到数据列：{data_column}")
            print(f"原始数据量：{len(raw_data)}")
            print(f"有效数值数据量：{len(clean_data)}")
            print(f"数据预览：{clean_data.head().tolist()}")

            return clean_data
        else:
            print(f"未找到数据列：{data_column}")
            return None

    except Exception as e:
        print(f"读取Excel文件时出错：{e}")
        return None

def generate_normal_distribution_plot(values, data_name="数据"):
    """
    根据数据生成正态分布图
    """
    if values is None or len(values) == 0:
        print("没有有效数据！")
        return

    # 计算统计信息
    mean = np.mean(values)
    std = np.std(values)

    print(f"\n数据统计信息：")
    print(f"均值：{mean:.4f}")
    print(f"标准差：{std:.4f}")
    print(f"样本数量：{len(values)}")
    print(f"最小值：{values.min():.4f}")
    print(f"最大值：{values.max():.4f}")

    # 创建图形 - 只显示正态分布图
    plt.figure(figsize=(10, 6))

    # 直方图 + 正态分布拟合曲线
    plt.hist(values, bins=50, density=True, alpha=0.7, color='skyblue', edgecolor='black', label='数据分布')

    # 生成正态分布曲线
    x = np.linspace(values.min(), values.max(), 100)
    y = stats.norm.pdf(x, mean, std)
    plt.plot(x, y, 'r-', linewidth=3, label=f'正态分布拟合\n均值={mean:.2f}\n标准差={std:.2f}')

    plt.xlabel('数值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.title(f'{data_name} - 正态分布图', fontsize=14, fontweight='bold')
    plt.legend(fontsize=11)
    plt.grid(True, alpha=0.3)

    # 保存图片
    output_file = f'{data_name}_正态分布图.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"\n图片已保存为：{output_file}")

    # 关闭图形以释放内存
    plt.close()

    # 进行正态性检验（如果样本量不太大）
    if len(values) <= 5000:  # Shapiro-Wilk检验对大样本不适用
        shapiro_stat, shapiro_p = stats.shapiro(values)
        print(f"\nShapiro-Wilk正态性检验：")
        print(f"统计量：{shapiro_stat:.4f}")
        print(f"p值：{shapiro_p:.4f}")
        if shapiro_p > 0.05:
            print("数据符合正态分布（p > 0.05）")
        else:
            print("数据不符合正态分布（p <= 0.05）")
    else:
        # 对于大样本，使用Kolmogorov-Smirnov检验
        ks_stat, ks_p = stats.kstest(values, lambda x: stats.norm.cdf(x, mean, std))
        print(f"\nKolmogorov-Smirnov正态性检验：")
        print(f"统计量：{ks_stat:.4f}")
        print(f"p值：{ks_p:.4f}")
        if ks_p > 0.05:
            print("数据符合正态分布（p > 0.05）")
        else:
            print("数据不符合正态分布（p <= 0.05）")

def main():
    """
    主函数
    """
    excel_file = "正太分布.xlsx"

    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return

    # 读取Excel数据
    values = read_excel_data(excel_file)
    if values is None:
        return

    # 生成正态分布图
    generate_normal_distribution_plot(values, "正态分布数据")

if __name__ == '__main__':
    main()
