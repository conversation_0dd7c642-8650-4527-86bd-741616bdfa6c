#!/usr/bin/env dash

echo "=== Testing merge fixes ==="

# Create a clean test directory
mkdir -p test_merge_dir
cd test_merge_dir

# Test subset2_33 scenario
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1
python3 ../mygit-checkout trunk
sed -Ei 's/5/24/' 7.txt
python3 ../mygit-commit -a -m commit-2

echo "Testing merge without message:"
python3 ../mygit-merge b1

echo ""
echo "Testing merge with unknown branch:"
python3 ../mygit-merge non-existent-branch -m message

echo ""
echo "Testing merge with conflict:"
python3 ../mygit-merge b1 -m message

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_merge_dir
