#!/usr/bin/env dash


echo "=== Test 2: mygit-commit and mygit-log ==="

rm -rf .mygit *.txt
python3 mygit-init > /dev/null

echo "Test content" > test.txt
python3 mygit-add test.txt

python3 mygit-commit -m "First commit"
if [ $? -eq 0 ]; then
    echo " mygit-commit succeeded"
else
    echo " mygit-commit failed"
    exit 1
fi

python3 mygit-log | grep "0 First commit" > /dev/null
if [ $? -eq 0 ]; then
    echo " mygit-log shows commit"
else
    echo " mygit-log failed"
    exit 1
fi

echo "=== Test 2 PASSED ==="
