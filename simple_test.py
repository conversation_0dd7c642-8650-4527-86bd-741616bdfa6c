"""
简单测试验证代码正确性
"""

# 测试Q1的Dijkstra算法
def test_dijkstra():
    print("测试Dijkstra算法...")
    
    import heapq
    from collections import defaultdict
    
    def dijkstra(G, start, end):
        distances = defaultdict(lambda: float('inf'))
        distances[start] = 0
        previous = {}
        pq = [(0, start)]
        visited = set()
        
        while pq:
            current_dist, current = heapq.heappop(pq)
            
            if current in visited:
                continue
                
            visited.add(current)
            
            if current == end:
                # Reconstruct path
                path = []
                node = end
                while node is not None:
                    path.append(node)
                    node = previous.get(node)
                return path[::-1], current_dist
            
            if current not in G:
                continue
                
            for neighbor, weight in G[current]:
                if neighbor in visited:
                    continue
                    
                new_dist = current_dist + weight
                if new_dist < distances[neighbor]:
                    distances[neighbor] = new_dist
                    previous[neighbor] = current
                    heapq.heappush(pq, (new_dist, neighbor))
        
        return None, float('inf')
    
    # 测试图
    G = {
        'A': [('B', 1), ('C', 4)],
        'B': [('C', 2), ('D', 5)],
        'C': [('D', 1)],
        'D': []
    }
    
    path, dist = dijkstra(G, 'A', 'D')
    print(f"最短路径: {path}, 距离: {dist}")
    
    if path == ['A', 'B', 'C', 'D'] and dist == 4:
        print("✅ Dijkstra测试通过")
        return True
    else:
        print("❌ Dijkstra测试失败")
        return False

# 测试Q2的k-core算法
def test_k_core():
    print("\n测试k-core算法...")
    
    def find_k_cores(adj_list, k):
        if not adj_list:
            return []
        
        vertices = list(adj_list.keys())
        if not vertices:
            return []
        
        # Compute degrees
        degrees = {}
        for v in vertices:
            degrees[v] = len(adj_list[v])
        
        # Iteratively remove vertices with degree < k
        remaining = set(vertices)
        changed = True
        
        while changed:
            changed = False
            to_remove = []
            
            for v in remaining:
                if degrees[v] < k:
                    to_remove.append(v)
            
            for v in to_remove:
                remaining.remove(v)
                changed = True
                
                # Update neighbors' degrees
                for u in adj_list[v]:
                    if u in remaining:
                        degrees[u] -= 1
        
        return list(remaining)
    
    # 测试图: 三角形 + 一个叶子节点
    adj_list = {
        1: [2, 3],
        2: [1, 3],
        3: [1, 2, 4],
        4: [3]
    }
    
    k1_core = find_k_cores(adj_list, 1)
    k2_core = find_k_cores(adj_list, 2)
    
    print(f"1-core: {k1_core}")
    print(f"2-core: {k2_core}")
    
    if len(k1_core) == 4 and len(k2_core) == 3:
        print("✅ k-core测试通过")
        return True
    else:
        print("❌ k-core测试失败")
        return False

def main():
    print("🧪 简单算法测试")
    print("=" * 40)
    
    dijkstra_ok = test_dijkstra()
    k_core_ok = test_k_core()
    
    print("\n" + "=" * 40)
    if dijkstra_ok and k_core_ok:
        print("🎉 所有核心算法测试通过!")
        print("✅ Q1和Q2的核心算法都能正常工作")
    else:
        print("❌ 部分算法测试失败")

if __name__ == "__main__":
    main()
