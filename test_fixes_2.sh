#!/usr/bin/env dash

echo "=== Testing fixes for subset2_21 (branch) ==="

rm -rf .mygit
rm -f a b c d e

# Test branch before first commit
python3 mygit-init
python3 mygit-branch

echo ""
echo "=== Testing fixes for subset1_22 (rm add rm show) ==="

rm -rf .mygit
rm -f a

python3 mygit-init
echo hello > a
python3 mygit-add a
python3 mygit-commit -m commit-0
python3 mygit-rm a
python3 mygit-status
python3 mygit-commit -m commit-1

echo ""
echo "=== Testing fixes for subset1_23 (status) ==="

rm -rf .mygit
rm -f a b c

python3 mygit-init
echo hi > a
python3 mygit-add a
python3 mygit-commit -m message
echo hello > b
echo hola > c
python3 mygit-add b c
python3 mygit-status
echo there >> b
rm c
python3 mygit-status

echo ""
echo "=== All tests completed ==="
