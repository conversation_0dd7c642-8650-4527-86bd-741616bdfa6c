import heapq
from array import array
class KShortestPathsQuery(object):
    def __init__(self):
        pass
    @staticmethod
    def query(G, s, t, k):
        if s == t:
            return [[s]] if k > 0 else []
        edge_weights = {}
        adj_out = [[] for _ in range(G.vertex_num)]
        for u in range(G.vertex_num):
            for v, w in G.adj_list_out[u]:
                edge_weights[(u, v)] = w
                adj_out[u].append((v, w))
        dist, prev = KShortestPathsQuery._dijkstra(adj_out, s, t, G.vertex_num)
        if dist[t] == float('inf'):
            return []
        first_path = []
        curr = t
        while curr != -1:
            first_path.append(curr)
            curr = prev[curr]
        first_path.reverse()
        
        if k == 1:
            return [first_path]
        A = [first_path]  
        B = []  
        path_set = {tuple(first_path)}  
        target_dist = [float('inf')] * G.vertex_num
        target_prev = [-1] * G.vertex_num
        KShortestPathsQuery._compute_target_distances(adj_out, t, target_dist, target_prev, G.vertex_num)
        while len(A) < k:
            prev_path = A[-1]
            for i in range(len(prev_path) - 1):
                spur_node = prev_path[i]
                root_path = prev_path[:i+1]
                excluded_edges = set()
                for path in A:
                    if len(path) > i + 1 and path[:i+1] == root_path:
                        excluded_edges.add((path[i], path[i+1]))
                excluded_nodes = set(root_path[:-1])
                spur_dist, spur_prev = KShortestPathsQuery._dijkstra_with_exclusions(
                    adj_out, spur_node, t, excluded_nodes, excluded_edges, G.vertex_num, target_dist
                )
                if spur_dist[t] != float('inf'):
                    spur_path = []
                    curr = t
                    while curr != -1 and curr != spur_node:
                        spur_path.append(curr)
                        curr = spur_prev[curr]
                    spur_path.append(spur_node)
                    spur_path.reverse()
                    candidate = root_path[:-1] + spur_path
                    weight = 0
                    for j in range(len(candidate) - 1):
                        weight += edge_weights[(candidate[j], candidate[j+1])]
                    candidate_tuple = tuple(candidate)
                    if candidate_tuple not in path_set:
                        path_set.add(candidate_tuple)
                        heapq.heappush(B, (weight, candidate_tuple, candidate))
            if not B:
                break
            _, _, next_path = heapq.heappop(B)
            A.append(next_path)
        return A
    @staticmethod
    def _dijkstra(adj_list, start, target, n):
        dist = array('d', [float('inf')] * n)
        dist[start] = 0
        prev = array('i', [-1] * n)
        visited = [False] * n
        pq = [(0, start)]
        while pq:
            d, u = heapq.heappop(pq)
            if u == target:
                break
            if visited[u]:
                continue
            visited[u] = True
            for v, w in adj_list[u]:
                if not visited[v]:
                    new_dist = dist[u] + w
                    if new_dist < dist[v]:
                        dist[v] = new_dist
                        prev[v] = u
                        heapq.heappush(pq, (new_dist, v))
        return dist, prev
    @staticmethod
    def _compute_target_distances(adj_list, target, dist, prev, n):
        rev_adj = [[] for _ in range(n)]
        for u in range(n):
            for v, w in adj_list[u]:
                rev_adj[v].append((u, w))
        dist[target] = 0
        visited = [False] * n
        pq = [(0, target)]
        while pq:
            d, u = heapq.heappop(pq)
            if visited[u]:
                continue
            visited[u] = True
            for v, w in rev_adj[u]:
                if not visited[v]:
                    new_dist = dist[u] + w
                    if new_dist < dist[v]:
                        dist[v] = new_dist
                        prev[v] = u
                        heapq.heappush(pq, (new_dist, v))
    @staticmethod
    def _dijkstra_with_exclusions(adj_list, start, target, excluded_nodes, excluded_edges, n, target_dist):
        dist = array('d', [float('inf')] * n)
        dist[start] = 0
        prev = array('i', [-1] * n)
        visited = [False] * n
        for node in excluded_nodes:
            if node != target:
                visited[node] = True
        pq = [(0, start)]
        while pq:
            d, u = heapq.heappop(pq)
            if u == target:
                            break
            if visited[u]:
                continue
            if dist[u] + target_dist[u] > dist[target] and dist[target] != float('inf'):
                continue
            visited[u] = True
            for v, w in adj_list[u]:
                if (u, v) in excluded_edges or visited[v]:
                    continue
                new_dist = dist[u] + w
                if new_dist < dist[v]:
                    dist[v] = new_dist
                    prev[v] = u
                    f_score = new_dist + target_dist[v]
                    heapq.heappush(pq, (f_score, v))
        return dist, prev
