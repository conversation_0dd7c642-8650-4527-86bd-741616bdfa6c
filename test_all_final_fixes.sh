#!/usr/bin/env dash

echo "=== Testing all final fixes ==="

# Create clean test directory
mkdir -p test_all_final_dir
cd test_all_final_dir

echo "1. Testing subset1_18 (rm --cached error):"
python3 ../mygit-init
echo 1 > a
echo 2 > b
python3 ../mygit-add a b
python3 ../mygit-commit -m "first"
echo 3 >> b
python3 ../mygit-add b
echo 4 > b
python3 ../mygit-rm --cached b

echo ""
echo "2. Testing subset1_23 (status):"
rm -rf .mygit *
python3 ../mygit-init
echo hi > a
python3 ../mygit-add a
python3 ../mygit-commit -m message
echo hello > b
echo hola > c
python3 ../mygit-add b c
echo there >> b
rm c
python3 ../mygit-status | grep "^[abc] -"

echo ""
echo "3. Testing subset2_24 (checkout error):"
rm -rf .mygit *
python3 ../mygit-init
touch a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-checkout non-existent

echo ""
echo "4. Testing subset2_25 (checkout file restore):"
rm -rf .mygit *
python3 ../mygit-init
echo line 1 > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
echo line 2 >> a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-1
python3 ../mygit-checkout b1
echo "File a after checkout to b1:"
cat a

echo ""
echo "5. Testing subset2_26 (checkout preserve changes):"
rm -rf .mygit *
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch b1
echo world >> a
python3 ../mygit-checkout b1
echo "File a after checkout to b1:"
cat a

echo ""
echo "6. Testing subset2_29 (checkout conflict):"
rm -rf .mygit *
python3 ../mygit-init
echo hello > a
python3 ../mygit-add a
python3 ../mygit-commit -m commit-A
python3 ../mygit-branch branchA
echo world > b
python3 ../mygit-add b
python3 ../mygit-commit -m commit-B
python3 ../mygit-checkout branchA
echo "new contents" > b
python3 ../mygit-checkout trunk

echo ""
echo "7. Testing subset2_30 (successful merge):"
rm -rf .mygit *
python3 ../mygit-init
seq 1 7 > 7.txt
python3 ../mygit-add 7.txt
python3 ../mygit-commit -m commit-0
python3 ../mygit-branch b1
python3 ../mygit-checkout b1
sed -Ei 's/2/42/' 7.txt
python3 ../mygit-commit -a -m commit-1
python3 ../mygit-checkout trunk
python3 ../mygit-merge b1 -m merge-message
echo "File 7.txt after merge:"
cat 7.txt

echo ""
echo "=== All tests completed ==="

# Clean up
cd ..
rm -rf test_all_final_dir
