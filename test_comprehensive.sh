#!/usr/bin/env dash

echo "=== Comprehensive test of all fixes ==="

# Create a clean test directory
mkdir -p test_comprehensive_dir
cd test_comprehensive_dir

echo "1. Testing branch before first commit:"
python3 ../mygit-init
python3 ../mygit-branch

echo ""
echo "2. Testing rm and commit:"
touch a b
python3 ../mygit-add a b
python3 ../mygit-commit -m "first commit"
python3 ../mygit-branch branch1
python3 ../mygit-branch branch2
python3 ../mygit-branch trunk
python3 ../mygit-branch
python3 ../mygit-branch -d branch2
python3 ../mygit-branch -d trunk
python3 ../mygit-branch -d b1
python3 ../mygit-branch

echo ""
echo "3. Testing checkout file management:"
python3 ../mygit-checkout branch1
touch c
python3 ../mygit-add c
python3 ../mygit-commit -m "branch1 commit"
python3 ../mygit-checkout trunk
echo "Files after checkout:"
ls

echo ""
echo "4. Testing merge errors:"
python3 ../mygit-merge branch1
python3 ../mygit-merge non-existent -m msg
python3 ../mygit-merge branch1 -m "merge msg"

echo ""
echo "=== Test completed ==="

# Clean up
cd ..
rm -rf test_comprehensive_dir
